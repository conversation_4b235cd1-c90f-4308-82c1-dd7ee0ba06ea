/**
 * 测试Node.js版本的网易云EAPI实现
 */

const NeteaseEAPI = require('./netease-eapi');

async function testNeteaseEAPI() {
  const api = new NeteaseEAPI();
  
  // 测试歌曲ID
  const songId = '186016';
  
  // 测试不同音质等级
  const levels = ['standard', 'exhigh', 'lossless', 'hires'];
  
  // 您的Cookie
  const cookieString = 'MUSIC_U=1eb9ce22024bb666e99b6743b2222f29ef64a9e88fda0fd5754714b900a5d70d993166e004087dd3b95085f6a85b059f5e9aba41e3f2646e3cebdbec0317df58c119e5;os=pc;appver=8.9.75;';
  
  const parsedCookies = api.parseCookie(cookieString);
  const cookies = api.createFullCookieObject(parsedCookies);
  
  console.log('🔍 开始测试网易云EAPI...\n');
  console.log('Cookie信息:');
  console.log('- 是否包含MUSIC_U:', cookieString.includes('MUSIC_U='));
  console.log('- Cookie长度:', cookieString.length);
  console.log('');
  
  for (const level of levels) {
    try {
      console.log(`🎵 测试音质: ${level}`);
      
      const result = await api.url_v1(songId, level, cookies);
      
      if (result && result.data && result.data.length > 0) {
        const songData = result.data[0];
        
        console.log(`✅ 成功获取 ${level} 音质:`);
        console.log(`   - URL: ${songData.url ? '有效' : '无效'}`);
        console.log(`   - 比特率: ${songData.br || '未知'}`);
        console.log(`   - 文件大小: ${songData.size ? (songData.size / 1024 / 1024).toFixed(2) + 'MB' : '未知'}`);
        console.log(`   - 音质代码: ${songData.code || '未知'}`);
        
        // 检查是否真的获取到了高音质
        if (level === 'lossless' && songData.br && songData.br >= 900000) {
          console.log(`🎉 成功获取无损音质! 比特率: ${songData.br}`);
        } else if (level === 'lossless') {
          console.log(`⚠️  请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`);
        }
      } else {
        console.log(`❌ ${level} 音质获取失败`);
        console.log('   响应:', JSON.stringify(result, null, 2));
      }
      
      console.log('');
      
    } catch (error) {
      console.log(`❌ ${level} 音质测试出错:`, error.message);
      console.log('');
    }
  }
}

// 运行测试
if (require.main === module) {
  testNeteaseEAPI().catch(console.error);
}

module.exports = testNeteaseEAPI;
