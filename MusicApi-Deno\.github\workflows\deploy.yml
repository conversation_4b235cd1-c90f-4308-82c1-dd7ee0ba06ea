name: Deploy to <PERSON><PERSON> Deploy

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    
    permissions:
      id-token: write # Needed for auth with Deno Deploy
      contents: read # Needed to clone the repository

    steps:
      - name: Clone repository
        uses: actions/checkout@v4

      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.x

      - name: Verify the project
        run: deno check main.ts

      - name: Deploy to Den<PERSON> Deploy
        uses: denoland/deployctl@v1
        with:
          project: "your-project-name" # 替换为您的项目名称
          entrypoint: "main.ts"
          root: "."
