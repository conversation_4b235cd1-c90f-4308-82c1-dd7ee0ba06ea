{"name": "netease-music-api-ts", "version": "1.0.0", "description": "TypeScript版本的网易云音乐API - 支持无损音质解析", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node server.ts", "test": "ts-node test.ts"}, "keywords": ["netease", "music", "api", "typescript", "lossless"], "author": "Claude 4.0 sonnet", "license": "MIT", "dependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0"}, "engines": {"node": ">=18.0.0"}}