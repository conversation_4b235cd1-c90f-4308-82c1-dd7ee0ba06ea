/**
 * Deno TypeScript服务器 - 网易云音乐无损解析
 */

import { serve } from "@std/http/server";
import { NeteaseEAPI, type QualityLevel, type SongInfo } from "./netease-eapi.ts";

const PORT = 3001;
const api = new NeteaseEAPI();

// 您的Cookie配置
const NETEASE_COOKIE = 'MUSIC_U=00ADBFC16B463B48ECD360EA8E4F17E08D3C84F11BB143279EAF360E6B271A7B454E26A57FC8E319CEF1E667B696337D2DA3E8EE638B26B1AD180920972F1D4FB3205352501003C7115132A9FC61E916056DAD41C72073C7401128AD0B7074CD165E934457B38E9DEBFCBC464AB38DD1858C006E634F1029C27CA7033D9B211884E0083D6943FBF1735F70D7BFF33B50B8C1216796ACC293ECF7B2ADF8661988B05D009C9DB0BFD465864FCD7511A6BA0F28ADB971B02DD9C9B73F7696B7106FB8C1D6E6F79BEF185B6522DA69C15C74FD8F0A854C1FA8AF34011ACE82F403AD80BC44E9485373633E2C0A6CCC51051C404CF2273D6036FFF4ABFBA7A5A919962C1C0D9FD1F95FDADEDE7B29CDDEB3D08EF28783D293F11531EAC72768CE5B38119E919BB12B422C4A7701319F24660C062E97D3FA222ABDC5837D09951548013567509A0CB73478B0DAA7F0A4299F17B8B93483B3F21A771C0506F266956BFF02726500FCB7197C99B30D7F7E3815DC61;os=pc;appver=8.9.75;';

// 主页HTML
const getHomePage = (): string => `
<!DOCTYPE html>
<html>
<head>
    <title>Deno TypeScript网易云音乐解析器</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            max-width: 900px; 
            margin: 0 auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            background: rgba(255, 255, 255, 0.95); 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        h1 { 
            color: #333; 
            text-align: center; 
            margin-bottom: 10px;
            font-size: 2.2em;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .form-group { 
            margin: 20px 0; 
        }
        label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #333;
        }
        input, select { 
            width: 100%; 
            padding: 12px 15px; 
            border: 2px solid #e1e5e9; 
            border-radius: 8px; 
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        button { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .result { 
            margin-top: 25px; 
            padding: 20px; 
            border-radius: 10px; 
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .success { 
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-left: 5px solid #00d4aa;
        }
        .error { 
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            border-left: 5px solid #ff6b6b;
        }
        .loading { 
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            text-align: center;
        }
        .download-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            margin-top: 10px;
            transition: background 0.3s ease;
        }
        .download-link:hover {
            background: rgba(255,255,255,0.3);
        }
        details {
            margin-top: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            padding: 10px;
        }
        summary {
            cursor: pointer;
            font-weight: 600;
            padding: 5px;
        }
        pre {
            background: rgba(0,0,0,0.1);
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .tech-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 Deno TypeScript音乐解析器</h1>
        <p class="subtitle">支持网易云音乐无损音质解析 | 基于TypeScript + Deno</p>
        
        <div class="tech-info">
            <strong>🚀 技术特性：</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>✅ TypeScript类型安全</li>
                <li>✅ Deno现代运行时</li>
                <li>✅ 完整EAPI加密算法</li>
                <li>✅ 支持无损音质解析</li>
            </ul>
        </div>
        
        <div class="form-group">
            <label for="songId">🎼 歌曲ID:</label>
            <input type="text" id="songId" value="5257138" placeholder="请输入网易云音乐歌曲ID">
        </div>
        
        <div class="form-group">
            <label for="level">🎧 音质等级:</label>
            <select id="level">
                <option value="standard">标准音质 (128kbps)</option>
                <option value="exhigh">极高音质 (320kbps)</option>
                <option value="lossless" selected>无损音质 (FLAC)</option>
                <option value="hires">Hi-Res音质</option>
                <option value="sky">沉浸环绕声</option>
                <option value="jyeffect">高清环绕声</option>
                <option value="jymaster">超清母带</option>
            </select>
        </div>
        
        <button onclick="parseSong()">🔍 解析歌曲</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function parseSong() {
            const songId = document.getElementById('songId').value;
            const level = document.getElementById('level').value;
            const resultDiv = document.getElementById('result');
            
            if (!songId.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入歌曲ID</h3></div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result loading">🔄 正在解析，请稍候...</div>';
            
            try {
                const response = await fetch('/api/netease/song', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ ids: songId, level: level })
                });
                
                const data = await response.json();
                
                if (data.status === 200) {
                    const isLossless = data.debug?.isLossless;
                    const qualityIcon = isLossless ? '🎉' : '🎵';
                    
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>\${qualityIcon} 解析成功</h3>
                            <p><strong>🎼 歌曲名:</strong> \${data.name}</p>
                            <p><strong>🎤 歌手:</strong> \${data.ar_name}</p>
                            <p><strong>💿 专辑:</strong> \${data.al_name}</p>
                            <p><strong>🎧 音质:</strong> \${data.level}</p>
                            <p><strong>📦 大小:</strong> \${data.size}</p>
                            <p><strong>📊 比特率:</strong> \${data.br || '未知'} bps</p>
                            \${data.url ? \`<a href="\${data.url}" target="_blank" class="download-link">⬇️ 下载音乐</a>\` : ''}
                            \${data.note ? \`<p><strong>💡 注意:</strong> \${data.note}</p>\` : ''}
                            \${data.debug ? \`<details><summary>🔧 调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <h3>❌ 解析失败</h3>
                            <p>\${data.error || '未知错误'}</p>
                            \${data.debug ? \`<details><summary>🔧 调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                        </div>
                    \`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`
                    <div class="result error">
                        <h3>❌ 请求失败</h3>
                        <p>\${error.message}</p>
                    </div>
                \`;
            }
        }
        
        // 回车键支持
        document.getElementById('songId').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                parseSong();
            }
        });
    </script>
</body>
</html>
`;

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url || '', true);
  const pathname = parsedUrl.pathname;

  try {
    if (pathname === '/') {
      // 主页
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(getHomePage());
      
    } else if (pathname === '/api/netease/song' && req.method === 'POST') {
      // API端点
      const body = await req.json();
      const { ids, url: songUrl, level = 'standard' } = body;
      const songId = ids || songUrl;
      
      if (!songId) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供歌曲ID'
        }), {
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
      
      console.log(`🎵 开始解析歌曲: ${songId}, 音质: ${level}`);
      
      // 解析Cookie
      const parsedCookies = api.parseCookie(NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);
      
      // 调用EAPI
      const result = await api.url_v1(songId, level as QualityLevel, cookies);
      
      console.log('📡 EAPI响应:', JSON.stringify(result, null, 2));
      
      if (!result || !result.data || !Array.isArray(result.data) || result.data.length === 0) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲信息',
          debug: { result }
        }), {
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
      
      const songData = result.data[0];
      
      if (!songData.url) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲URL，可能是版权限制或需要会员权限',
          debug: { songData }
        }), {
          status: 400,
          headers: { 
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
      
      // 构建响应
      const responseData: SongInfo = {
        status: 200,
        name: `歌曲ID: ${songId}`,
        ar_name: '网易云音乐',
        al_name: '专辑信息',
        level: api.getQualityName(level as QualityLevel),
        size: api.formatFileSize(songData.size),
        url: songData.url.replace('http://', 'https://'),
        br: songData.br,
        debug: {
          requestedLevel: level,
          actualBr: songData.br,
          actualSize: songData.size,
          code: songData.code,
          type: songData.type,
          isLossless: api.isLosslessQuality(songData.br)
        }
      };
      
      // 检查是否真的获取到了无损音质
      if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
        responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
        console.log(`🎉 成功获取无损音质! 比特率: ${songData.br}`);
      } else if (level === 'lossless') {
        responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
        console.log(`⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`);
      }
      
      return new Response(JSON.stringify(responseData), {
        headers: { 
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
      
    } else {
      // 404
      return new Response('Not Found', { 
        status: 404,
        headers: corsHeaders
      });
    }
    
  } catch (error) {
    console.error('❌ 服务器错误:', error);
    return new Response(JSON.stringify({
      status: 500,
      error: `服务器错误: ${error.message}`,
      debug: { error: error.toString() }
    }), {
      status: 500,
      headers: { 
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    });
  }
}

// 启动服务器
console.log(`🚀 Deno TypeScript网易云音乐解析服务器启动中...`);
console.log(`📡 服务地址: http://localhost:${PORT}`);
console.log(`🎵 API端点: http://localhost:${PORT}/api/netease/song`);
console.log(`🔍 Cookie状态: ${NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 包含会员Cookie' : '❌ 缺少会员Cookie'}`);
console.log(`🎯 TypeScript版本，支持类型安全和现代特性`);
console.log('');
console.log('请在浏览器中访问 http://localhost:3001 开始测试!');

await serve(handler, { port: PORT });
