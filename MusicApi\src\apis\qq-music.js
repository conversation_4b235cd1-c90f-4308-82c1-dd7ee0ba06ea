/**
 * QQ音乐API模块
 * 移植Python版本的QQ音乐解析功能
 */

import { CryptoUtils, HttpClient, URLParser, Utils } from '../utils/index.js';

export class QQMusicAPI {
  // QQ音乐API基础配置
  static get BASE_CONFIG() {
    return {
      base_url: 'https://u.y.qq.com/cgi-bin/musicu.fcg',
      song_url: 'https://c.y.qq.com/v8/fcg-bin/fcg_play_single_song.fcg',
      lyric_url: 'https://c.y.qq.com/lyric/fcgi-bin/fcg_query_lyric_new.fcg',
      guid: '10000',
      uin: '0'
    };
  }

  // 文件格式配置 - 移植自Python版本
  static get FILE_CONFIG() {
    return {
      '128': { s: 'M500', e: '.mp3', bitrate: '128kbps' },
      '320': { s: 'M800', e: '.mp3', bitrate: '320kbps' },
      'flac': { s: 'F000', e: '.flac', bitrate: 'FLAC' },
      'master': { s: 'AI00', e: '.flac', bitrate: 'Master' },
      'atmos_2': { s: 'Q000', e: '.flac', bitrate: 'Atmos 2' },
      'atmos_51': { s: 'Q001', e: '.flac', bitrate: 'Atmos 5.1' },
      'ogg_640': { s: 'O801', e: '.ogg', bitrate: '640kbps' },
      'ogg_320': { s: 'O800', e: '.ogg', bitrate: '320kbps' },
      'ogg_192': { s: 'O600', e: '.ogg', bitrate: '192kbps' },
      'ogg_96': { s: 'O400', e: '.ogg', bitrate: '96kbps' },
      'aac_192': { s: 'C600', e: '.m4a', bitrate: '192kbps' },
      'aac_96': { s: 'C400', e: '.m4a', bitrate: '96kbps' },
      'aac_48': { s: 'C200', e: '.m4a', bitrate: '48kbps' }
    };
  }

  /**
   * 从URL中提取歌曲ID - 移植ids函数
   * @param {string} url - QQ音乐URL
   * @returns {Promise<string|null>} 歌曲ID
   */
  static async extractSongId(url) {
    try {
      return await URLParser.parseQQ(url);
    } catch (error) {
      throw new Error(`QQ音乐URL解析失败: ${error.message}`);
    }
  }

  /**
   * 获取音乐播放URL - 移植get_music_url函数
   * @param {string} songmid - 歌曲MID
   * @param {string} fileType - 文件类型
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object|null>} 音乐URL信息
   */
  static async getMusicUrl(songmid, fileType = 'flac', cookies = {}) {
    if (!this.FILE_CONFIG[fileType]) {
      throw new Error(`无效的文件类型: ${fileType}`);
    }

    const fileInfo = this.FILE_CONFIG[fileType];
    const filename = `${fileInfo.s}${songmid}${songmid}${fileInfo.e}`;

    const reqData = {
      req_1: {
        module: 'vkey.GetVkeyServer',
        method: 'CgiGetVkey',
        param: {
          filename: [filename],
          guid: this.BASE_CONFIG.guid,
          songmid: [songmid],
          songtype: [0],
          uin: this.BASE_CONFIG.uin,
          loginflag: 1,
          platform: '20'
        }
      },
      loginUin: this.BASE_CONFIG.uin,
      comm: {
        uin: this.BASE_CONFIG.uin,
        format: 'json',
        ct: 24,
        cv: 0
      }
    };

    try {
      const response = await HttpClient.requestQQ(
        this.BASE_CONFIG.base_url,
        reqData,
        cookies,
        'POST'
      );

      const purl = response.req_1?.data?.midurlinfo?.[0]?.purl;
      if (!purl || purl === '') {
        // VIP歌曲或无权限
        return null;
      }

      const baseUrl = response.req_1?.data?.sip?.[1] || '';
      const fullUrl = (baseUrl + purl).replace('http://', 'https://');
      
      // 根据前缀确定比特率
      const prefix = purl.substring(0, 4);
      const bitrate = Object.values(this.FILE_CONFIG)
        .find(config => config.s === prefix)?.bitrate || '';

      return {
        url: fullUrl,
        bitrate: bitrate
      };
    } catch (error) {
      throw new Error(`获取QQ音乐URL失败: ${error.message}`);
    }
  }

  /**
   * 获取歌曲信息 - 移植get_music_song函数
   * @param {string} mid - 歌曲MID
   * @param {number} sid - 歌曲ID
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 歌曲信息
   */
  static async getMusicSong(mid, sid, cookies = {}) {
    let reqData;
    
    if (sid && sid !== 0) {
      // 使用songid进行请求
      reqData = {
        songid: sid,
        platform: 'yqq',
        format: 'json'
      };
    } else {
      // 使用songmid进行请求
      reqData = {
        songmid: mid,
        platform: 'yqq',
        format: 'json'
      };
    }

    try {
      const response = await HttpClient.post(
        this.BASE_CONFIG.song_url,
        reqData,
        {
          headers: {
            ...HttpClient.QQ_HEADERS,
            Cookie: this.cookieObjectToString(cookies)
          }
        }
      );

      const data = await HttpClient.parseJsonResponse(response);

      if (data.data && data.data.length > 0) {
        const songInfo = data.data[0];
        const albumInfo = songInfo.album || {};
        const singers = songInfo.singer || [];
        const singerNames = singers.map(singer => singer.name || 'Unknown').join(', ');

        // 获取专辑封面图片URL
        const albumMid = albumInfo.mid;
        const imgUrl = albumMid 
          ? `https://y.qq.com/music/photo_new/T002R800x800M000${albumMid}.jpg?max_age=2592000`
          : 'https://axidiqolol53.objectstorage.ap-seoul-1.oci.customer-oci.com/n/axidiqolol53/b/lusic/o/resources/cover.jpg';

        return {
          name: songInfo.name || 'Unknown',
          album: albumInfo.name || 'Unknown',
          singer: singerNames,
          pic: imgUrl,
          mid: songInfo.mid || mid,
          id: songInfo.id || sid
        };
      } else {
        return { msg: '信息获取错误/歌曲不存在' };
      }
    } catch (error) {
      throw new Error(`获取QQ音乐歌曲信息失败: ${error.message}`);
    }
  }

  /**
   * 获取歌词 - 移植get_music_lyric_new函数
   * @param {string} songId - 歌曲ID
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 歌词信息
   */
  static async getMusicLyricNew(songId, cookies = {}) {
    const payload = {
      "music.musichallSong.PlayLyricInfo.GetPlayLyricInfo": {
        module: "music.musichallSong.PlayLyricInfo",
        method: "GetPlayLyricInfo",
        param: {
          trans_t: 0,
          roma_t: 0,
          crypt: 0,
          lrc_t: 0,
          interval: 208,
          trans: 1,
          ct: 6,
          singerName: "",
          type: 0,
          qrc_t: 0,
          cv: 80600,
          roma: 1,
          songID: songId,
          qrc: 0,
          albumName: "",
          songName: ""
        }
      },
      comm: {
        wid: "",
        tmeAppID: "qqmusic",
        authst: "",
        uid: "",
        gray: "0",
        OpenUDID: "",
        ct: "6",
        patch: "2",
        psrf_qqopenid: "",
        sid: "",
        psrf_access_token_expiresAt: "",
        cv: "80600",
        gzip: "0",
        qq: "",
        nettype: "2",
        psrf_qqunionid: "",
        psrf_qqaccess_token: "",
        tmeLoginType: "2"
      }
    };

    try {
      const response = await HttpClient.requestQQ(
        this.BASE_CONFIG.base_url,
        payload,
        cookies,
        'POST'
      );

      const lyricData = response["music.musichallSong.PlayLyricInfo.GetPlayLyricInfo"]?.data;
      
      if (lyricData && lyricData.lyric) {
        // 解码歌词
        const lyric = CryptoUtils.base64Decode(
          new TextEncoder().encode(lyricData.lyric)
        );
        const tylyric = lyricData.trans ? CryptoUtils.base64Decode(
          new TextEncoder().encode(lyricData.trans)
        ) : new Uint8Array();

        return {
          lyric: new TextDecoder().decode(lyric),
          tylyric: new TextDecoder().decode(tylyric)
        };
      } else {
        return {
          lyric: '',
          tylyric: ''
        };
      }
    } catch (error) {
      return { error: '无法获取歌词' };
    }
  }

  /**
   * 获取所有支持的音质格式
   * @returns {Array} 音质格式列表
   */
  static getSupportedFormats() {
    return Object.keys(this.FILE_CONFIG);
  }

  /**
   * 验证文件类型是否支持
   * @param {string} fileType - 文件类型
   * @returns {boolean} 是否支持
   */
  static isValidFileType(fileType) {
    return fileType in this.FILE_CONFIG;
  }

  /**
   * Cookie对象转字符串
   * @param {Object} cookies - Cookie对象
   * @returns {string} Cookie字符串
   */
  static cookieObjectToString(cookies) {
    if (!cookies || typeof cookies !== 'object') {
      return '';
    }
    
    return Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');
  }
}
