/**
 * 纯加密功能测试，不涉及网络请求
 */

const crypto = require('crypto');

console.log('🔍 Node.js加密功能测试');
console.log('Node.js版本:', process.version);
console.log('');

// 测试MD5哈希
function testMD5() {
  console.log('1. 测试MD5哈希...');
  const text = 'test';
  const hash = crypto.createHash('md5').update(text, 'utf8').digest('hex');
  console.log('   输入:', text);
  console.log('   MD5:', hash);
  console.log('   ✅ MD5功能正常');
  console.log('');
}

// 测试AES加密
function testAES() {
  console.log('2. 测试AES加密...');
  
  try {
    const AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
    const plaintext = 'hello world';
    
    console.log('   密钥:', AES_KEY.toString());
    console.log('   明文:', plaintext);
    
    // PKCS7填充
    const data = Buffer.from(plaintext, 'utf8');
    const blockSize = 16;
    const padLength = blockSize - (data.length % blockSize);
    const paddedData = Buffer.concat([data, Buffer.alloc(padLength, padLength)]);
    
    console.log('   填充前长度:', data.length);
    console.log('   填充后长度:', paddedData.length);

    // AES ECB加密
    const cipher = crypto.createCipheriv('aes-128-ecb', AES_KEY, null);
    cipher.setAutoPadding(false);
    
    let encrypted = cipher.update(paddedData);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const encryptedHex = encrypted.toString('hex');
    
    console.log('   加密结果:', encryptedHex);
    console.log('   ✅ AES加密功能正常');
    
  } catch (error) {
    console.log('   ❌ AES加密失败:', error.message);
  }
  
  console.log('');
}

// 测试完整的网易云加密流程
function testNeteaseEncryption() {
  console.log('3. 测试网易云加密流程...');
  
  try {
    const AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
    
    // 模拟网易云的参数
    const songId = '186016';
    const level = 'lossless';
    const config = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      "requestId": "12345678"
    };
    
    const payload = {
      'ids': [songId],
      'level': level,
      'encodeType': 'flac',
      'header': JSON.stringify(config)
    };
    
    const urlPath = '/api/song/enhance/player/url/v1';
    const payloadStr = JSON.stringify(payload);
    
    console.log('   URL路径:', urlPath);
    console.log('   Payload:', payloadStr);
    
    // MD5哈希
    const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
    const digest = crypto.createHash('md5').update(digestText, 'utf8').digest('hex');
    
    console.log('   哈希输入:', digestText.substring(0, 50) + '...');
    console.log('   MD5摘要:', digest);
    
    // 构建最终参数
    const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;
    console.log('   参数长度:', paramsText.length);
    
    // AES加密
    const data = Buffer.from(paramsText, 'utf8');
    const blockSize = 16;
    const padLength = blockSize - (data.length % blockSize);
    const paddedData = Buffer.concat([data, Buffer.alloc(padLength, padLength)]);
    
    const cipher = crypto.createCipheriv('aes-128-ecb', AES_KEY, null);
    cipher.setAutoPadding(false);
    
    let encrypted = cipher.update(paddedData);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const encryptedHex = encrypted.toString('hex');
    
    console.log('   最终加密结果长度:', encryptedHex.length);
    console.log('   加密结果预览:', encryptedHex.substring(0, 32) + '...');
    console.log('   ✅ 网易云加密流程完成');
    
  } catch (error) {
    console.log('   ❌ 网易云加密失败:', error.message);
  }
  
  console.log('');
}

// 运行所有测试
console.log('开始测试...\n');
testMD5();
testAES();
testNeteaseEncryption();
console.log('🎯 所有加密测试完成！');
console.log('如果所有测试都通过，说明Node.js可以正确实现网易云EAPI加密算法。');
