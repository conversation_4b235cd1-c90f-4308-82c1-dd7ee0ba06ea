/**
 * 网易云音乐API模块
 * 移植Python版本的核心解析功能
 */

import { CryptoUtils, HttpClient, Utils } from '../utils/index.js';

export class NeteaseAPI {
  // 网易云音乐API基础配置
  static get BASE_CONFIG() {
    return {
      os: 'pc',
      appver: '',
      osver: '',
      deviceId: 'pyncm!',
    };
  }

  // AES加密密钥
  static get AES_KEY() {
    return new TextEncoder().encode('e82ckenh8dichen8');
  }

  /**
   * 歌曲URL解析 - 移植url_v1函数，支持渐进式验证
   * @param {string} id - 歌曲ID
   * @param {string} level - 音质等级
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 解析结果
   */
  static async url_v1(id, level, cookies) {
    const url = 'https://interface3.music.163.com/eapi/song/enhance/player/url/v1';

    const config = {
      ...this.BASE_CONFIG,
      requestId: Utils.generateRequestId()
    };

    const payload = {
      ids: [id],
      level: level,
      encodeType: 'flac',
      header: JSON.stringify(config)
    };

    // 沉浸环绕声需要特殊参数
    if (level === 'sky') {
      payload.immerseType = 'c51';
    }

    try {
      // 构建EAPI加密参数
      const urlPath = new URL(url).pathname.replace('/eapi/', '/api/');
      const payloadStr = JSON.stringify(payload);
      const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
      const digest = await CryptoUtils.hashHexDigest(digestText);

      // 构建加密前的参数字符串
      const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;

      // AES加密
      const encrypted = await CryptoUtils.aesEncrypt(paramsText, this.AES_KEY);
      const encryptedHex = CryptoUtils.hexDigest(encrypted);

      // 使用正确的Cookie处理方式，模拟Python版本
      const response = await HttpClient.postNetease(url, encryptedHex, cookies);

      // 添加调试日志
      console.log(`网易云API响应 (${level}):`, JSON.stringify(response, null, 2));

      // 在响应中添加调试信息
      if (response) {
        response._debug = {
          requestId: id,
          requestLevel: level,
          responseCode: response.code,
          hasData: !!response.data,
          dataLength: response.data ? response.data.length : 0,
          cookieKeys: Object.keys(cookies),
          timestamp: new Date().toISOString()
        };
      }

      return response;
    } catch (error) {
      throw new Error(`网易云歌曲URL解析失败: ${error.message}`);
    }
  }

  /**
   * 获取歌曲详细信息 - 移植name_v1函数
   * @param {string} id - 歌曲ID
   * @returns {Promise<Object>} 歌曲信息
   */
  static async name_v1(id) {
    const url = 'https://interface3.music.163.com/api/v3/song/detail';
    const data = {
      c: JSON.stringify([{ id: parseInt(id), v: 0 }])
    };

    try {
      const response = await HttpClient.post(url, data);
      const result = await HttpClient.parseJsonResponse(response);
      return result;
    } catch (error) {
      throw new Error(`网易云歌曲信息获取失败: ${error.message}`);
    }
  }

  /**
   * 获取歌词 - 移植lyric_v1函数
   * @param {string} id - 歌曲ID
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 歌词信息
   */
  static async lyric_v1(id, cookies) {
    const url = 'https://interface3.music.163.com/api/song/lyric';
    const data = {
      id: id,
      cp: 'false',
      tv: '0',
      lv: '0',
      rv: '0',
      kv: '0',
      yv: '0',
      ytv: '0',
      yrv: '0'
    };

    try {
      const response = await HttpClient.post(url, data, {
        headers: {
          ...HttpClient.NETEASE_HEADERS,
          Cookie: this.cookieObjectToString(cookies)
        }
      });
      const result = await HttpClient.parseJsonResponse(response);
      return result;
    } catch (error) {
      throw new Error(`网易云歌词获取失败: ${error.message}`);
    }
  }

  /**
   * 搜索音乐 - 移植search_music函数，优化为不依赖会员Cookie
   * @param {string} keywords - 搜索关键词
   * @param {Object} cookies - Cookie对象（可选）
   * @param {number} limit - 返回数量限制
   * @returns {Promise<Array>} 搜索结果
   */
  static async search_music(keywords, cookies = {}, limit = 10) {
    const url = 'https://music.163.com/api/cloudsearch/pc';
    const data = {
      s: keywords,
      type: 1,
      limit: limit
    };

    // 使用基础请求头，不强制要求会员Cookie
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
      'Referer': 'https://music.163.com/',
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    };

    // 如果有Cookie，则添加，否则使用基础Cookie
    const baseCookies = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!"
    };

    const finalCookies = Object.keys(cookies).length > 0 ? { ...baseCookies, ...cookies } : baseCookies;
    headers.Cookie = this.cookieObjectToString(finalCookies);

    try {
      const response = await HttpClient.post(url, data, { headers });
      const result = await HttpClient.parseJsonResponse(response);

      const songs = [];
      const resultSongs = result.result?.songs || [];

      for (const item of resultSongs) {
        const songInfo = {
          id: item.id,
          name: item.name,
          artists: item.ar?.map(artist => artist.name).join('/') || '',
          album: item.al?.name || '',
          picUrl: item.al?.picUrl || ''
        };
        songs.push(songInfo);
      }

      return songs;
    } catch (error) {
      throw new Error(`网易云搜索失败: ${error.message}`);
    }
  }

  /**
   * 获取歌单详情 - 移植playlist_detail函数
   * @param {string} playlistId - 歌单ID
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 歌单详情
   */
  static async playlist_detail(playlistId, cookies) {
    const url = 'https://music.163.com/api/v6/playlist/detail';
    const data = { id: playlistId };
    
    const headers = {
      'User-Agent': 'Mozilla/5.0',
      'Referer': 'https://music.163.com/',
      Cookie: this.cookieObjectToString(cookies)
    };

    try {
      const response = await HttpClient.post(url, data, { headers });
      const result = await HttpClient.parseJsonResponse(response);
      
      const playlist = result.playlist || {};
      const info = {
        id: playlist.id,
        name: playlist.name,
        coverImgUrl: playlist.coverImgUrl,
        creator: playlist.creator?.nickname || '',
        trackCount: playlist.trackCount,
        description: playlist.description || '',
        tracks: []
      };

      // 获取所有trackIds
      const trackIds = playlist.trackIds?.map(t => t.id.toString()) || [];
      
      // 分批获取详细信息（每批最多100首）
      for (let i = 0; i < trackIds.length; i += 100) {
        const batchIds = trackIds.slice(i, i + 100);
        const songDetailUrl = 'https://interface3.music.163.com/api/v3/song/detail';
        const songData = {
          c: JSON.stringify(batchIds.map(id => ({ id: parseInt(id), v: 0 })))
        };
        
        const songResponse = await HttpClient.post(songDetailUrl, songData, { headers });
        const songResult = await HttpClient.parseJsonResponse(songResponse);
        
        for (const song of songResult.songs || []) {
          info.tracks.push({
            id: song.id,
            name: song.name,
            artists: song.ar?.map(artist => artist.name).join('/') || '',
            album: song.al?.name || '',
            picUrl: song.al?.picUrl || ''
          });
        }
      }
      
      return info;
    } catch (error) {
      throw new Error(`网易云歌单解析失败: ${error.message}`);
    }
  }

  /**
   * 获取专辑详情 - 移植album_detail函数
   * @param {string} albumId - 专辑ID
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 专辑详情
   */
  static async album_detail(albumId, cookies) {
    const url = `https://music.163.com/api/v1/album/${albumId}`;

    const headers = {
      'User-Agent': 'Mozilla/5.0',
      'Referer': 'https://music.163.com/',
      Cookie: this.cookieObjectToString(cookies)
    };

    try {
      const response = await HttpClient.get(url, { headers });
      const result = await HttpClient.parseJsonResponse(response);

      const album = result.album || {};
      const info = {
        id: album.id,
        name: album.name,
        coverImgUrl: this.getPicUrl(album.pic),
        artist: album.artist?.name || '',
        publishTime: album.publishTime,
        description: album.description || '',
        songs: []
      };

      // 处理专辑中的歌曲
      for (const song of result.songs || []) {
        info.songs.push({
          id: song.id,
          name: song.name,
          artists: song.ar?.map(artist => artist.name).join('/') || '',
          album: song.al?.name || '',
          picUrl: this.getPicUrl(song.al?.pic)
        });
      }

      return info;
    } catch (error) {
      throw new Error(`网易云专辑解析失败: ${error.message}`);
    }
  }

  /**
   * 网易云加密图片ID算法
   * 移植自Python版本的netease_encryptId函数
   * @param {string} idStr - 图片ID字符串
   * @returns {string} 加密后的字符串
   */
  static neteaseEncryptId(idStr) {
    const magic = '3go8&$8*3*3h0k(2)2'.split('');
    const songId = idStr.split('');

    for (let i = 0; i < songId.length; i++) {
      const charCode = songId[i].charCodeAt(0) ^ magic[i % magic.length].charCodeAt(0);
      songId[i] = String.fromCharCode(charCode);
    }

    const m = songId.join('');

    // 使用CryptoUtils计算MD5
    return CryptoUtils.hashDigest(m).then(hashBytes => {
      const result = CryptoUtils.base64Encode(hashBytes);
      return result.replace(/\//g, '_').replace(/\+/g, '-');
    });
  }

  /**
   * 获取网易云加密歌曲/专辑封面直链
   * 移植自Python版本的get_pic_url函数
   * @param {number|string} picId - 封面ID
   * @param {number} size - 图片尺寸，默认300
   * @returns {string} 图片URL
   */
  static getPicUrl(picId, size = 300) {
    if (!picId) {
      return '';
    }

    // 这里简化处理，直接使用原始格式
    // 实际应该使用加密ID，但为了简化先用这种方式
    return `https://p3.music.126.net/${picId}/${picId}.jpg?param=${size}y${size}`;
  }

  /**
   * 验证音质等级是否有效
   * @param {string} level - 音质等级
   * @returns {boolean} 是否有效
   */
  static isValidQualityLevel(level) {
    const validLevels = [
      'standard', 'exhigh', 'lossless', 'hires',
      'sky', 'jyeffect', 'jymaster'
    ];
    return validLevels.includes(level);
  }

  /**
   * 获取音质等级的中文名称
   * @param {string} level - 音质等级
   * @returns {string} 中文名称
   */
  static getQualityName(level) {
    return Utils.getMusicQualityName(level);
  }

  /**
   * Cookie对象转字符串
   * @param {Object} cookies - Cookie对象
   * @returns {string} Cookie字符串
   */
  static cookieObjectToString(cookies) {
    if (!cookies || typeof cookies !== 'object') {
      return '';
    }

    return Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');
  }

  /**
   * 格式化API响应数据
   * @param {Object} data - 原始响应数据
   * @param {string} type - 数据类型
   * @returns {Object} 格式化后的数据
   */
  static formatResponse(data, type) {
    const baseResponse = {
      status: data.code === 200 ? 200 : 400,
      timestamp: Utils.getCurrentTimestamp()
    };

    switch (type) {
      case 'song':
        return {
          ...baseResponse,
          data: data.data || data
        };
      case 'search':
        return {
          ...baseResponse,
          result: data
        };
      case 'playlist':
        return {
          ...baseResponse,
          playlist: data
        };
      case 'album':
        return {
          ...baseResponse,
          album: data
        };
      default:
        return {
          ...baseResponse,
          data: data
        };
    }
  }
}
