# 🎵 网易云音乐API - TypeScript版本

基于Deno运行时的TypeScript实现，支持网易云音乐无损音质解析。

## ✨ 特性

- 🚀 **TypeScript** - 完整的类型安全支持
- 🦕 **Deno运行时** - 现代JavaScript/TypeScript运行时
- 🔐 **完整EAPI加密** - 精确移植Python版本的加密算法
- 🎧 **无损音质支持** - 支持从标准到无损的所有音质等级
- 🌐 **Web界面** - 美观的现代化Web界面
- 📱 **响应式设计** - 支持移动端访问

## 🎯 支持的音质等级

| 音质等级 | 比特率 | 格式 | 说明 |
|---------|--------|------|------|
| standard | 128kbps | MP3 | 标准音质 |
| exhigh | 320kbps | MP3 | 极高音质 |
| **lossless** | **999kbps+** | **FLAC** | **无损音质** |
| hires | 1999kbps+ | FLAC | Hi-Res音质 |
| sky | 1999kbps+ | FLAC | 沉浸环绕声 |
| jyeffect | 999kbps+ | FLAC | 高清环绕声 |
| jymaster | 1999kbps+ | FLAC | 超清母带 |

## 🚀 快速开始

### 前置要求

- [Deno](https://deno.land/) 1.37+

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd MusicApi-TS
   ```

2. **配置Cookie**
   
   编辑 `server.ts` 文件，替换 `NETEASE_COOKIE` 变量为您的网易云音乐Cookie：
   ```typescript
   const NETEASE_COOKIE = 'MUSIC_U=your_music_u_cookie;os=pc;appver=8.9.75;';
   ```

3. **启动服务器**
   ```bash
   deno task start
   ```
   
   或者开发模式（自动重载）：
   ```bash
   deno task dev
   ```

4. **访问Web界面**
   
   打开浏览器访问：`http://localhost:3001`

### 运行测试

```bash
deno task test
```

## 📡 API接口

### POST /api/netease/song

解析网易云音乐歌曲，获取下载链接。

**请求体：**
```json
{
  "ids": "5257138",
  "level": "lossless"
}
```

**响应：**
```json
{
  "status": 200,
  "name": "歌曲名称",
  "ar_name": "歌手名称",
  "al_name": "专辑名称",
  "level": "无损音质",
  "size": "15.23MB",
  "url": "https://music.163.com/...",
  "br": 999000,
  "note": "🎉 成功获取无损音质! 比特率: 999000",
  "debug": {
    "requestedLevel": "lossless",
    "actualBr": 999000,
    "isLossless": true
  }
}
```

## 🔧 技术架构

### 核心组件

- **NeteaseEAPI类** - 网易云EAPI加密和请求处理
- **TypeScript类型定义** - 完整的类型安全支持
- **Deno HTTP服务器** - 基于标准库的HTTP服务
- **现代Web界面** - 响应式设计，支持移动端

### 加密算法

精确移植Python版本的EAPI加密算法：

1. **MD5哈希** - 使用Deno的Web Crypto API
2. **AES-128-ECB加密** - 通过CBC模式配合零IV实现
3. **PKCS7填充** - 标准的块加密填充
4. **参数格式** - 与Python版本完全一致

## 🎵 使用示例

### 基础用法

```typescript
import { NeteaseEAPI } from "./netease-eapi.ts";

const api = new NeteaseEAPI();
const cookies = api.createFullCookieObject(
  api.parseCookie("MUSIC_U=your_cookie;os=pc;appver=8.9.75;")
);

// 获取无损音质
const result = await api.url_v1("5257138", "lossless", cookies);
console.log(result);
```

### 检查音质

```typescript
if (api.isLosslessQuality(result.data[0].br)) {
  console.log("🎉 成功获取无损音质!");
}
```

## 🔍 调试和故障排除

### 常见问题

1. **Cookie配置错误**
   - 确保包含完整的`MUSIC_U`值
   - 检查Cookie是否过期

2. **歌曲无法解析**
   - 尝试不同的歌曲ID
   - 检查歌曲是否有版权限制

3. **无法获取无损音质**
   - 确认账号有会员权限
   - 检查歌曲是否支持无损格式

### 调试模式

启动时会显示详细的调试信息：
```
🚀 Deno TypeScript网易云音乐解析服务器启动中...
📡 服务地址: http://localhost:3001
🎵 API端点: http://localhost:3001/api/netease/song
🔍 Cookie状态: ✅ 包含会员Cookie
🎯 TypeScript版本，支持类型安全和现代特性
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## ⚠️ 免责声明

本项目仅供学习和研究使用，请遵守相关法律法规和服务条款。
