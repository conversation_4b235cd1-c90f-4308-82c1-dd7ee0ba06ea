/**
 * 简化的测试脚本，验证基础加密功能
 */

const crypto = require('crypto');

console.log('🔍 开始测试Node.js加密功能...\n');

// 测试MD5哈希
function testMD5() {
  const text = 'nobody/api/song/enhance/player/url/v1use{"ids":[186016],"level":"lossless","encodeType":"flac","header":"{}"}md5forencrypt';
  const hash = crypto.createHash('md5').update(text, 'utf8').digest('hex');
  console.log('✅ MD5哈希测试:');
  console.log('   输入:', text.substring(0, 50) + '...');
  console.log('   输出:', hash);
  console.log('');
  return hash;
}

// 测试AES加密
function testAES() {
  const AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
  const plaintext = '/api/song/enhance/player/url/v1-36cd479b6b5-{"test":"data"}-36cd479b6b5-abcd1234';
  
  try {
    // PKCS7填充
    const data = Buffer.from(plaintext, 'utf8');
    const blockSize = 16;
    const padLength = blockSize - (data.length % blockSize);
    const paddedData = Buffer.concat([data, Buffer.alloc(padLength, padLength)]);

    // AES ECB加密
    const cipher = crypto.createCipheriv('aes-128-ecb', AES_KEY, null);
    cipher.setAutoPadding(false);
    
    let encrypted = cipher.update(paddedData);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    const encryptedHex = encrypted.toString('hex');
    
    console.log('✅ AES加密测试:');
    console.log('   密钥:', AES_KEY.toString());
    console.log('   明文长度:', plaintext.length);
    console.log('   填充后长度:', paddedData.length);
    console.log('   加密结果长度:', encrypted.length);
    console.log('   十六进制:', encryptedHex.substring(0, 32) + '...');
    console.log('');
    
    return encryptedHex;
  } catch (error) {
    console.log('❌ AES加密失败:', error.message);
    console.log('');
    return null;
  }
}

// 测试网络请求
async function testNetworkRequest() {
  const https = require('https');
  const querystring = require('querystring');
  
  console.log('🌐 测试网络请求...');
  
  return new Promise((resolve) => {
    const postData = querystring.stringify({ test: 'data' });
    
    const options = {
      hostname: 'httpbin.org',
      port: 443,
      path: '/post',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        console.log('✅ 网络请求成功');
        console.log('   状态码:', res.statusCode);
        console.log('   响应长度:', data.length);
        console.log('');
        resolve(true);
      });
    });

    req.on('error', (error) => {
      console.log('❌ 网络请求失败:', error.message);
      console.log('');
      resolve(false);
    });

    req.on('timeout', () => {
      console.log('❌ 网络请求超时');
      console.log('');
      req.destroy();
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

// 运行所有测试
async function runTests() {
  console.log('Node.js版本:', process.version);
  console.log('');
  
  // 测试基础功能
  testMD5();
  testAES();
  
  // 测试网络功能
  await testNetworkRequest();
  
  console.log('🎯 基础功能测试完成！');
  console.log('如果所有测试都通过，说明Node.js环境可以正确实现网易云EAPI加密。');
}

runTests().catch(console.error);
