/**
 * 工具模块统一导出
 * 提供所有共享工具类的便捷访问
 */

export { URLParser } from './url-parser.js';
export { CryptoUtils } from './crypto.js';
export { CookieManager } from './cookie-manager.js';
export { HttpClient } from './http-client.js';

/**
 * 工具函数集合
 * 提供一些常用的辅助函数
 */
export class Utils {
  /**
   * 格式化文件大小
   * 移植自Python版本的size()函数
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小字符串
   */
  static formatFileSize(bytes) {
    if (typeof bytes !== 'number' || bytes < 0) {
      return '0B';
    }

    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const size = 1024.0;
    
    for (let i = 0; i < units.length; i++) {
      if (bytes / size < 1) {
        return `${bytes.toFixed(2)}${units[i]}`;
      }
      bytes = bytes / size;
    }
    
    return `${bytes.toFixed(2)}${units[units.length - 1]}`;
  }

  /**
   * 音质等级转换为中文描述
   * 移植自Python版本的music_level1()函数
   * @param {string} level - 音质等级
   * @returns {string} 中文描述
   */
  static getMusicQualityName(level) {
    const levels = {
      'standard': '标准音质',
      'exhigh': '极高音质',
      'lossless': '无损音质',
      'hires': 'Hi-Res音质',
      'sky': '沉浸环绕声',
      'jyeffect': '高清环绕声',
      'jymaster': '超清母带',
      // QQ音乐格式
      '128': '128kbps',
      '320': '320kbps',
      'flac': 'FLAC无损',
      'master': '母带音质',
      'atmos_2': 'Atmos 2.0',
      'atmos_51': 'Atmos 5.1'
    };
    
    return levels[level] || '未知音质';
  }

  /**
   * 验证音质等级是否有效
   * @param {string} level - 音质等级
   * @param {string} platform - 平台类型
   * @returns {boolean} 是否有效
   */
  static isValidQualityLevel(level, platform) {
    const neteaseQualities = [
      'standard', 'exhigh', 'lossless', 'hires', 
      'sky', 'jyeffect', 'jymaster'
    ];
    
    const qqQualities = [
      '128', '320', 'flac', 'master', 
      'atmos_2', 'atmos_51', 'ogg_640', 'ogg_320', 
      'ogg_192', 'ogg_96', 'aac_192', 'aac_96', 'aac_48'
    ];
    
    switch (platform) {
      case 'netease':
        return neteaseQualities.includes(level);
      case 'qq':
        return qqQualities.includes(level);
      default:
        return false;
    }
  }

  /**
   * 生成随机请求ID
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {string} 随机ID字符串
   */
  static generateRequestId(min = 20000000, max = 30000000) {
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }

  /**
   * 延迟执行
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @param {*} defaultValue - 默认值
   * @returns {*} 解析结果或默认值
   */
  static safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      return defaultValue;
    }
  }

  /**
   * 检查字符串是否为空
   * @param {string} str - 待检查的字符串
   * @returns {boolean} 是否为空
   */
  static isEmpty(str) {
    return !str || typeof str !== 'string' || str.trim().length === 0;
  }

  /**
   * 清理URL参数
   * @param {string} url - 原始URL
   * @returns {string} 清理后的URL
   */
  static cleanUrl(url) {
    if (!url || typeof url !== 'string') {
      return '';
    }
    
    return url.trim().replace(/[<>]/g, '');
  }

  /**
   * 验证URL格式
   * @param {string} url - 待验证的URL
   * @returns {boolean} 是否为有效URL
   */
  static isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取当前时间戳
   * @returns {number} 时间戳（毫秒）
   */
  static getCurrentTimestamp() {
    return Date.now();
  }

  /**
   * 格式化时间戳为可读字符串
   * @param {number} timestamp - 时间戳
   * @returns {string} 格式化后的时间字符串
   */
  static formatTimestamp(timestamp) {
    return new Date(timestamp).toISOString();
  }
}
