/**
 * TypeScript版本测试脚本
 */

import { NeteaseEAPI, type QualityLevel } from "./netease-eapi.ts";

async function testNeteaseEAPI() {
  console.log('🔍 开始测试Deno TypeScript版本的网易云EAPI...\n');
  
  const api = new NeteaseEAPI();
  
  // 测试歌曲ID
  const songIds = ['5257138', '186016', '27646205'];
  
  // 测试不同音质等级
  const levels: QualityLevel[] = ['standard', 'exhigh', 'lossless'];
  
  // Cookie配置
  const cookieString = 'MUSIC_U=00ADBFC16B463B48ECD360EA8E4F17E08D3C84F11BB143279EAF360E6B271A7B454E26A57FC8E319CEF1E667B696337D2DA3E8EE638B26B1AD180920972F1D4FB3205352501003C7115132A9FC61E916056DAD41C72073C7401128AD0B7074CD165E934457B38E9DEBFCBC464AB38DD1858C006E634F1029C27CA7033D9B211884E0083D6943FBF1735F70D7BFF33B50B8C1216796ACC293ECF7B2ADF8661988B05D009C9DB0BFD465864FCD7511A6BA0F28ADB971B02DD9C9B73F7696B7106FB8C1D6E6F79BEF185B6522DA69C15C74FD8F0A854C1FA8AF34011ACE82F403AD80BC44E9485373633E2C0A6CCC51051C404CF2273D6036FFF4ABFBA7A5A919962C1C0D9FD1F95FDADEDE7B29CDDEB3D08EF28783D293F11531EAC72768CE5B38119E919BB12B422C4A7701319F24660C062E97D3FA222ABDC5837D09951548013567509A0CB73478B0DAA7F0A4299F17B8B93483B3F21A771C0506F266956BFF02726500FCB7197C99B30D7F7E3815DC61;os=pc;appver=8.9.75;';
  
  const parsedCookies = api.parseCookie(cookieString);
  const cookies = api.createFullCookieObject(parsedCookies);
  
  console.log('📋 测试配置:');
  console.log('   Cookie长度:', cookieString.length);
  console.log('   是否包含MUSIC_U:', cookieString.includes('MUSIC_U='));
  console.log('   测试歌曲数量:', songIds.length);
  console.log('   测试音质数量:', levels.length);
  console.log('');
  
  for (const songId of songIds) {
    console.log(`🎵 测试歌曲ID: ${songId}`);
    
    for (const level of levels) {
      try {
        console.log(`  🎧 测试音质: ${level}`);
        
        const result = await api.url_v1(songId, level, cookies);
        
        if (result && result.data && result.data.length > 0) {
          const songData = result.data[0];
          
          if (songData.url) {
            console.log(`    ✅ 成功获取 ${api.getQualityName(level)}:`);
            console.log(`       比特率: ${songData.br || '未知'}`);
            console.log(`       文件大小: ${api.formatFileSize(songData.size)}`);
            console.log(`       URL状态: 有效`);
            
            // 检查是否真的获取到了无损音质
            if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
              console.log(`    🎉 成功获取无损音质! 比特率: ${songData.br}`);
            }
          } else {
            console.log(`    ❌ ${api.getQualityName(level)} 获取失败 (code: ${songData.code})`);
          }
        } else {
          console.log(`    ❌ ${api.getQualityName(level)} 无响应数据`);
        }
        
      } catch (error) {
        console.log(`    ❌ ${api.getQualityName(level)} 测试出错: ${error.message}`);
      }
    }
    
    console.log('');
  }
  
  console.log('🎯 TypeScript版本测试完成!');
}

// 运行测试
if (import.meta.main) {
  await testNeteaseEAPI();
}
