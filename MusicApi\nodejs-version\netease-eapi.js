/**
 * Node.js版本的网易云EAPI完整实现
 * 精确移植Python版本的加密算法
 */

const crypto = require('crypto');
const https = require('https');
const querystring = require('querystring');

class NeteaseEAPI {
  constructor() {
    this.AES_KEY = Buffer.from('e82ckenh8dichen8', 'utf8');
    this.BASE_URL = 'https://interface3.music.163.com';
  }

  /**
   * 十六进制摘要 - 精确移植Python的HexDigest
   * @param {Buffer} data - 字节数据
   * @returns {string} 十六进制字符串
   */
  hexDigest(data) {
    return Array.from(data)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * MD5哈希摘要 - 精确移植Python的HashDigest
   * @param {string} text - 待哈希的文本
   * @returns {Buffer} MD5哈希值
   */
  hashDigest(text) {
    return crypto.createHash('md5').update(text, 'utf8').digest();
  }

  /**
   * MD5哈希十六进制摘要 - 精确移植Python的HashHexDigest
   * @param {string} text - 待哈希的文本
   * @returns {string} MD5哈希值的十六进制字符串
   */
  hashHexDigest(text) {
    const hashBytes = this.hashDigest(text);
    return this.hexDigest(hashBytes);
  }

  /**
   * AES ECB加密 - 精确移植Python版本
   * @param {string} plaintext - 明文
   * @returns {Buffer} 加密后的字节数组
   */
  aesEncrypt(plaintext) {
    // PKCS7填充
    const data = Buffer.from(plaintext, 'utf8');
    const blockSize = 16;
    const padLength = blockSize - (data.length % blockSize);
    const paddedData = Buffer.concat([data, Buffer.alloc(padLength, padLength)]);

    // AES ECB加密 - 使用正确的Node.js crypto API
    const cipher = crypto.createCipheriv('aes-128-ecb', this.AES_KEY, null);
    cipher.setAutoPadding(false); // 我们已经手动填充了

    let encrypted = cipher.update(paddedData);
    encrypted = Buffer.concat([encrypted, cipher.final()]);

    return encrypted;
  }

  /**
   * 网易云EAPI请求 - 精确移植Python版本
   * @param {string} songId - 歌曲ID
   * @param {string} level - 音质等级
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} API响应
   */
  async url_v1(songId, level, cookies) {
    const url = `${this.BASE_URL}/eapi/song/enhance/player/url/v1`;
    
    // 构建配置 - 与Python版本完全一致
    const config = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      "requestId": Math.floor(Math.random() * (30000000 - 20000000 + 1) + 20000000).toString()
    };

    // 构建payload - 与Python版本完全一致
    const payload = {
      'ids': [songId],
      'level': level,
      'encodeType': 'flac',
      'header': JSON.stringify(config)
    };

    // 沉浸环绕声特殊处理
    if (level === 'sky') {
      payload['immerseType'] = 'c51';
    }

    // 构建加密字符串 - 精确移植Python第70-78行
    const urlPath = '/api/song/enhance/player/url/v1';
    const payloadStr = JSON.stringify(payload);
    const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
    const digest = this.hashHexDigest(digestText);
    const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;

    // AES加密
    const encrypted = this.aesEncrypt(paramsText);
    const encryptedHex = this.hexDigest(encrypted);

    // 构建Cookie字符串
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    // 发送请求
    return new Promise((resolve, reject) => {
      const postData = querystring.stringify({ params: encryptedHex });
      
      const options = {
        hostname: 'interface3.music.163.com',
        port: 443,
        path: '/eapi/song/enhance/player/url/v1',
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': '',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Content-Length': Buffer.byteLength(postData),
          'Cookie': cookieString
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        res.on('end', () => {
          try {
            const result = JSON.parse(data);
            resolve(result);
          } catch (error) {
            reject(new Error(`JSON解析失败: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }

  /**
   * 解析Cookie字符串
   * @param {string} cookieString - Cookie字符串
   * @returns {Object} Cookie对象
   */
  parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        cookies[key] = value;
      }
    });
    return cookies;
  }

  /**
   * 创建完整的Cookie对象
   * @param {Object} parsedCookies - 解析后的Cookie
   * @returns {Object} 完整的Cookie对象
   */
  createFullCookieObject(parsedCookies) {
    return {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      ...parsedCookies
    };
  }
}

module.exports = NeteaseEAPI;
