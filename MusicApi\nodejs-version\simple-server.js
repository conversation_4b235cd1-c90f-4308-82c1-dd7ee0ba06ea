/**
 * 纯Node.js服务器 - 无外部依赖
 * 测试无损音质解析功能
 */

const http = require('http');
const url = require('url');
const querystring = require('querystring');
const NeteaseEAPI = require('./netease-eapi');

const PORT = 3000;
const api = new NeteaseEAPI();

// 您的Cookie配置
const NETEASE_COOKIE = 'MUSIC_U=1eb9ce22024bb666e99b6743b2222f29ef64a9e88fda0fd5754714b900a5d70d993166e004087dd3b95085f6a85b059f5e9aba41e3f2646e3cebdbec0317df58c119e5;os=pc;appver=8.9.75;';

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  
  try {
    if (pathname === '/') {
      // 主页
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Node.js网易云音乐解析器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                .container { background: #f5f5f5; padding: 20px; border-radius: 10px; }
                .form-group { margin: 15px 0; }
                label { display: block; margin-bottom: 5px; font-weight: bold; }
                input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; box-sizing: border-box; }
                button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
                button:hover { background: #0056b3; }
                .result { margin-top: 20px; padding: 15px; background: white; border-radius: 5px; }
                .success { border-left: 4px solid #28a745; }
                .error { border-left: 4px solid #dc3545; }
                .loading { color: #007bff; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎵 Node.js网易云音乐解析器</h1>
                <p>测试无损音质解析功能</p>
                
                <div class="form-group">
                    <label for="songId">歌曲ID:</label>
                    <input type="text" id="songId" value="186016" placeholder="请输入歌曲ID">
                </div>
                
                <div class="form-group">
                    <label for="level">音质等级:</label>
                    <select id="level">
                        <option value="standard">标准音质 (128kbps)</option>
                        <option value="exhigh">极高音质 (320kbps)</option>
                        <option value="lossless" selected>无损音质 (FLAC)</option>
                        <option value="hires">Hi-Res音质</option>
                    </select>
                </div>
                
                <button onclick="parseSong()">🔍 解析歌曲</button>
                
                <div id="result"></div>
            </div>

            <script>
                async function parseSong() {
                    const songId = document.getElementById('songId').value;
                    const level = document.getElementById('level').value;
                    const resultDiv = document.getElementById('result');
                    
                    resultDiv.innerHTML = '<div class="result loading">🔄 正在解析，请稍候...</div>';
                    
                    try {
                        const response = await fetch('/api/netease/song', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ ids: songId, level: level })
                        });
                        
                        const data = await response.json();
                        
                        if (data.status === 200) {
                            resultDiv.innerHTML = \`
                                <div class="result success">
                                    <h3>✅ 解析成功</h3>
                                    <p><strong>歌曲名:</strong> \${data.name}</p>
                                    <p><strong>歌手:</strong> \${data.ar_name}</p>
                                    <p><strong>专辑:</strong> \${data.al_name}</p>
                                    <p><strong>音质:</strong> \${data.level}</p>
                                    <p><strong>大小:</strong> \${data.size}</p>
                                    <p><strong>比特率:</strong> \${data.br || '未知'}</p>
                                    \${data.url ? \`<p><strong>下载链接:</strong> <a href="\${data.url}" target="_blank">点击下载</a></p>\` : ''}
                                    \${data.note ? \`<p><strong>注意:</strong> \${data.note}</p>\` : ''}
                                    \${data.debug ? \`<details><summary>调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                                </div>
                            \`;
                        } else {
                            resultDiv.innerHTML = \`
                                <div class="result error">
                                    <h3>❌ 解析失败</h3>
                                    <p>\${data.error || '未知错误'}</p>
                                    \${data.debug ? \`<details><summary>调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                                </div>
                            \`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = \`
                            <div class="result error">
                                <h3>❌ 请求失败</h3>
                                <p>\${error.message}</p>
                            </div>
                        \`;
                    }
                }
            </script>
        </body>
        </html>
      `);
      
    } else if (pathname === '/api/netease/song' && req.method === 'POST') {
      // API端点
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', async () => {
        try {
          const data = JSON.parse(body);
          const { ids, url: songUrl, level = 'standard' } = data;
          const songId = ids || songUrl;
          
          if (!songId) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              status: 400,
              error: '必须提供歌曲ID'
            }));
            return;
          }
          
          console.log(`🎵 开始解析歌曲: ${songId}, 音质: ${level}`);
          
          // 解析Cookie
          const parsedCookies = api.parseCookie(NETEASE_COOKIE);
          const cookies = api.createFullCookieObject(parsedCookies);
          
          // 调用EAPI
          const result = await api.url_v1(songId, level, cookies);
          
          console.log('📡 EAPI响应:', JSON.stringify(result, null, 2));
          
          if (!result || !result.data || !Array.isArray(result.data) || result.data.length === 0) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              status: 400,
              error: '无法获取歌曲信息',
              debug: { result }
            }));
            return;
          }
          
          const songData = result.data[0];
          
          if (!songData.url) {
            res.writeHead(400, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
              status: 400,
              error: '无法获取歌曲URL，可能是版权限制或需要会员权限',
              debug: { songData }
            }));
            return;
          }
          
          // 音质等级中文名称
          const qualityNames = {
            'standard': '标准音质',
            'exhigh': '极高音质', 
            'lossless': '无损音质',
            'hires': 'Hi-Res音质'
          };
          
          // 构建响应
          const responseData = {
            status: 200,
            name: `歌曲ID: ${songId}`,
            ar_name: '网易云音乐',
            al_name: '专辑信息',
            level: qualityNames[level] || '未知音质',
            size: songData.size ? `${(songData.size / 1024 / 1024).toFixed(2)}MB` : '未知大小',
            url: songData.url.replace('http://', 'https://'),
            br: songData.br,
            debug: {
              requestedLevel: level,
              actualBr: songData.br,
              actualSize: songData.size,
              code: songData.code,
              type: songData.type,
              isLossless: songData.br && songData.br >= 900000
            }
          };
          
          // 检查是否真的获取到了无损音质
          if (level === 'lossless' && songData.br && songData.br >= 900000) {
            responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
            console.log(`🎉 成功获取无损音质! 比特率: ${songData.br}`);
          } else if (level === 'lossless') {
            responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
            console.log(`⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`);
          }
          
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(responseData));
          
        } catch (error) {
          console.error('❌ 解析失败:', error);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            status: 500,
            error: `解析失败: ${error.message}`,
            debug: { error: error.toString() }
          }));
        }
      });
      
    } else {
      // 404
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
    }
    
  } catch (error) {
    console.error('服务器错误:', error);
    res.writeHead(500, { 'Content-Type': 'text/plain' });
    res.end('Internal Server Error');
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log(`🚀 Node.js网易云音乐解析服务器已启动!`);
  console.log(`📡 服务地址: http://localhost:${PORT}`);
  console.log(`🎵 API端点: http://localhost:${PORT}/api/netease/song`);
  console.log(`🔍 Cookie状态: ${NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 包含会员Cookie' : '❌ 缺少会员Cookie'}`);
  console.log('');
  console.log('请在浏览器中访问 http://localhost:3000 开始测试!');
});
