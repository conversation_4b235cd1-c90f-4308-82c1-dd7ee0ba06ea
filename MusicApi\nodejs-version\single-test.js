/**
 * 单个API调用测试
 */

const NeteaseEAPI = require('./netease-eapi');

async function testSingleAPI() {
  console.log('🎵 测试单个网易云EAPI调用...\n');
  
  const api = new NeteaseEAPI();
  const songId = '186016';
  const level = 'lossless';
  
  // Cookie配置
  const cookieString = 'MUSIC_U=1eb9ce22024bb666e99b6743b2222f29ef64a9e88fda0fd5754714b900a5d70d993166e004087dd3b95085f6a85b059f5e9aba41e3f2646e3cebdbec0317df58c119e5;os=pc;appver=8.9.75;';
  const parsedCookies = api.parseCookie(cookieString);
  const cookies = api.createFullCookieObject(parsedCookies);
  
  console.log('📋 测试参数:');
  console.log('   歌曲ID:', songId);
  console.log('   音质等级:', level);
  console.log('   Cookie长度:', cookieString.length);
  console.log('   是否包含MUSIC_U:', cookieString.includes('MUSIC_U='));
  console.log('');
  
  try {
    console.log('🔄 开始调用EAPI...');
    
    // 设置超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), 15000);
    });
    
    const apiPromise = api.url_v1(songId, level, cookies);
    
    const result = await Promise.race([apiPromise, timeoutPromise]);
    
    console.log('✅ EAPI调用成功!');
    console.log('');
    
    if (result && result.data && result.data.length > 0) {
      const songData = result.data[0];
      
      console.log('🎶 歌曲信息:');
      console.log('   歌曲ID:', songData.id || '未知');
      console.log('   URL状态:', songData.url ? '有效' : '无效');
      console.log('   比特率:', songData.br || '未知');
      console.log('   文件大小:', songData.size ? (songData.size / 1024 / 1024).toFixed(2) + 'MB' : '未知');
      console.log('   音质代码:', songData.code || '未知');
      console.log('   类型:', songData.type || '未知');
      console.log('');
      
      // 检查是否获取到无损音质
      if (songData.br && songData.br >= 900000) {
        console.log('🎉 成功获取无损音质!');
        console.log('   比特率:', songData.br);
        console.log('   文件大小:', (songData.size / 1024 / 1024).toFixed(2) + 'MB');
      } else {
        console.log('⚠️  未获取到无损音质');
        console.log('   实际比特率:', songData.br || '未知');
        console.log('   可能原因: Cookie权限不足或歌曲不支持无损');
      }
      
      console.log('');
      console.log('📄 完整响应:');
      console.log(JSON.stringify(result, null, 2));
      
    } else {
      console.log('❌ 未获取到有效的歌曲数据');
      console.log('响应:', JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.log('❌ EAPI调用失败:', error.message);
    
    if (error.message.includes('ENOTFOUND') || error.message.includes('timeout')) {
      console.log('   可能原因: 网络连接问题');
    } else if (error.message.includes('JSON')) {
      console.log('   可能原因: 服务器返回非JSON数据');
    } else {
      console.log('   错误详情:', error);
    }
  }
}

// 运行测试
testSingleAPI().catch(console.error);
