/**
 * Deno Deploy主入口文件
 * 统一的服务器实现，自动适配本地和Deno Deploy环境
 */

import { NeteaseEAPI, type QualityLevel, type SongInfo } from "./netease-eapi.ts";

// 环境检测
const isDenoDeploy = !!Deno.env.get('DENO_DEPLOYMENT_ID');

// 配置管理
interface Config {
  NETEASE_COOKIE: string;
  PORT: number;
  DEBUG: boolean;
  SEARCH_LIMIT: number;
  DOWNLOAD_CONCURRENCY: number;
}

// 加载配置
function loadConfig(): Config {
  const config: Config = {
    NETEASE_COOKIE: Deno.env.get('NETEASE_COOKIE') ||
      'MUSIC_U=00ADBFC16B463B48ECD360EA8E4F17E08D3C84F11BB143279EAF360E6B271A7B454E26A57FC8E319CEF1E667B696337D2DA3E8EE638B26B1AD180920972F1D4FB3205352501003C7115132A9FC61E916056DAD41C72073C7401128AD0B7074CD165E934457B38E9DEBFCBC464AB38DD1858C006E634F1029C27CA7033D9B211884E0083D6943FBF1735F70D7BFF33B50B8C1216796ACC293ECF7B2ADF8661988B05D009C9DB0BFD465864FCD7511A6BA0F28ADB971B02DD9C9B73F7696B7106FB8C1D6E6F79BEF185B6522DA69C15C74FD8F0A854C1FA8AF34011ACE82F403AD80BC44E9485373633E2C0A6CCC51051C404CF2273D6036FFF4ABFBA7A5A919962C1C0D9FD1F95FDADEDE7B29CDDEB3D08EF28783D293F11531EAC72768CE5B38119E919BB12B422C4A7701319F24660C062E97D3FA222ABDC5837D09951548013567509A0CB73478B0DAA7F0A4299F17B8B93483B3F21A771C0506F266956BFF02726500FCB7197C99B30D7F7E3815DC61;os=pc;appver=8.9.75;',
    PORT: parseInt(Deno.env.get('PORT') || (isDenoDeploy ? '8000' : '3002')),
    DEBUG: (Deno.env.get('DEBUG') || 'false').toLowerCase() === 'true',
    SEARCH_LIMIT: parseInt(Deno.env.get('SEARCH_LIMIT') || '50'),
    DOWNLOAD_CONCURRENCY: parseInt(Deno.env.get('DOWNLOAD_CONCURRENCY') || '3')
  };

  // 验证Cookie
  if (!config.NETEASE_COOKIE.includes('MUSIC_U=')) {
    console.error('❌ 错误：未配置有效的NETEASE_COOKIE');
    if (isDenoDeploy) {
      console.log('🌐 请在Deno Deploy控制台设置环境变量 NETEASE_COOKIE');
    } else {
      console.log('💻 请设置环境变量：export NETEASE_COOKIE="your_cookie"');
    }
  }

  return config;
}

const config = loadConfig();
const api = new NeteaseEAPI();

// 主页HTML
const getHomePage = (): string => `
<!DOCTYPE html>
<html>
<head>
    <title>🦕 ${isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析器</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            max-width: 1000px; 
            margin: 0 auto; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            background: rgba(255, 255, 255, 0.95); 
            padding: 30px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .header { text-align: center; margin-bottom: 30px; }
        h1 { 
            color: #333; 
            margin-bottom: 10px;
            font-size: 2.5em;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .subtitle { color: #666; font-size: 1.2em; margin-bottom: 20px; }
        .badge {
            display: inline-block;
            background: linear-gradient(135deg, ${isDenoDeploy ? '#00d4aa 0%, #00a8ff 100%' : '#667eea 0%, #764ba2 100%'});
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            margin: 5px;
        }
        .tabs {
            display: flex;
            margin: 20px 0;
            border-bottom: 2px solid #e1e5e9;
        }
        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            width: auto;
        }
        .tab-button:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .form-group { margin: 20px 0; }
        label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #333;
            font-size: 1.1em;
        }
        input, select, textarea { 
            width: 100%; 
            padding: 15px 20px; 
            border: 2px solid #e1e5e9; 
            border-radius: 10px; 
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        button { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 18px 35px; 
            border: none; 
            border-radius: 12px; 
            cursor: pointer; 
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        button:hover { 
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .result { 
            margin-top: 30px; 
            padding: 25px; 
            border-radius: 15px; 
            animation: slideIn 0.5s ease;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .success { 
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-left: 6px solid #00d4aa;
        }
        .error { 
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            border-left: 6px solid #ff6b6b;
        }
        .loading { 
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            text-align: center;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .info-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid rgba(255,255,255,0.5);
        }
        .download-link {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            margin: 15px 0;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        .download-link:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .search-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-top: 15px;
        }
        .search-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .search-item:hover { background: #f8f9fa; }
        .search-item:last-child { border-bottom: none; }
        .song-info { flex: 1; }
        .song-name { font-weight: 600; color: #333; margin-bottom: 5px; }
        .song-artist { color: #666; font-size: 0.9em; }
        .song-actions { display: flex; gap: 10px; align-items: center; }
        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .play-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .play-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .download-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
        }
        .quality-selector {
            margin-left: 10px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }
        .audio-player {
            width: 100%;
            margin-top: 10px;
            border-radius: 8px;
        }

        /* Apple Music风格播放器 */
        .music-player {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        .music-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        .album-art {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            margin: 0 auto 20px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .song-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .song-artist {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 25px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .player-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }
        .play-pause-btn {
            width: 70px;
            height: 70px;
            font-size: 30px;
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }
        .progress-container {
            margin: 20px 0;
            position: relative;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }
        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 3px;
            transition: width 0.1s ease;
            width: 0%;
        }
        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 8px;
            opacity: 0.8;
        }
        .volume-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }
        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }
        .close-player {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .close-player:hover {
            background: rgba(255,255,255,0.3);
        }
        .playlist-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-top: 15px;
        }
        .playlist-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .playlist-item:last-child { border-bottom: none; }
        .playlist-checkbox {
            width: 18px;
            height: 18px;
        }
        .playlist-info { flex: 1; }
        .playlist-name { font-weight: 500; color: #333; }
        .playlist-artist { color: #666; font-size: 0.9em; }
        .select-all-btn {
            margin: 10px 0;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
        }
        .batch-input { min-height: 120px; resize: vertical; }
        @media (max-width: 768px) {
            .container { padding: 20px; }
            h1 { font-size: 2em; }
            .info-grid { grid-template-columns: 1fr; }
            .tabs { flex-direction: column; }
            .tab-button { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦕 ${isDenoDeploy ? 'Deno Deploy' : 'Deno'} 音乐解析器</h1>
            <p class="subtitle">网易云音乐无损解析服务</p>
            <div>
                <span class="badge">${isDenoDeploy ? '🌐 Deno Deploy' : '💻 本地环境'}</span>
                <span class="badge">📘 TypeScript</span>
                <span class="badge">🔐 EAPI加密</span>
                <span class="badge">🎵 无损音质</span>
            </div>
        </div>
        
        <!-- 功能选项卡 -->
        <div class="tabs">
            <button class="tab-button active" onclick="showTab('single')">🎵 单曲解析</button>
            <button class="tab-button" onclick="showTab('search')">🔍 在线搜索</button>
            <button class="tab-button" onclick="showTab('batch')">📦 批量下载</button>
        </div>
        
        <!-- 单曲解析 -->
        <div id="single" class="tab-content active">
            <div class="form-section">
                <div class="form-group">
                    <label for="songId">🎼 歌曲ID</label>
                    <input type="text" id="songId" value="5257138" placeholder="请输入网易云音乐歌曲ID">
                </div>
                <div class="form-group">
                    <label for="level">🎧 音质等级</label>
                    <select id="level">
                        <option value="standard">标准音质 (128kbps)</option>
                        <option value="exhigh">极高音质 (320kbps)</option>
                        <option value="lossless" selected>无损音质 (FLAC)</option>
                        <option value="hires">Hi-Res音质</option>
                        <option value="sky">沉浸环绕声</option>
                        <option value="jyeffect">高清环绕声</option>
                        <option value="jymaster">超清母带</option>
                    </select>
                </div>
                <button onclick="parseSong()">🔍 解析歌曲</button>
            </div>
        </div>
        
        <!-- 在线搜索 -->
        <div id="search" class="tab-content">
            <div class="form-section">
                <div class="form-group">
                    <label for="searchKeyword">🔍 搜索关键词</label>
                    <input type="text" id="searchKeyword" placeholder="输入歌曲名、歌手名或专辑名">
                </div>
                <div class="form-group">
                    <label for="searchType">🎯 搜索类型</label>
                    <select id="searchType">
                        <option value="songs">歌曲</option>
                        <option value="artists">歌手</option>
                    </select>
                </div>
                <button onclick="searchMusic()">🔍 开始搜索</button>
                <div id="searchResults" class="search-results" style="display: none;"></div>
            </div>
        </div>
        
        <!-- 批量下载 -->
        <div id="batch" class="tab-content">
            <div class="form-section">
                <div class="form-group">
                    <label for="batchType">📦 批量类型</label>
                    <select id="batchType" onchange="updateBatchInterface()">
                        <option value="album">专辑ID</option>
                        <option value="playlist">歌单ID</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="batchInput">📝 输入ID</label>
                    <input type="text" id="batchInput" placeholder="请输入专辑或歌单ID">
                </div>
                <button onclick="loadPlaylist()">📋 加载歌曲列表</button>

                <div id="playlistContainer" style="display: none;">
                    <div class="form-group">
                        <button class="select-all-btn" onclick="toggleSelectAll()">🔄 全选/取消全选</button>
                        <div class="form-group">
                            <label for="batchLevel">🎧 音质等级</label>
                            <select id="batchLevel">
                                <option value="standard">标准音质 (128kbps)</option>
                                <option value="exhigh">极高音质 (320kbps)</option>
                                <option value="lossless" selected>无损音质 (FLAC)</option>
                                <option value="hires">Hi-Res音质</option>
                            </select>
                        </div>
                    </div>
                    <div class="playlist-container" id="playlistItems"></div>
                    <button onclick="downloadSelected()" style="margin-top: 15px;">📦 下载选中歌曲</button>
                </div>
            </div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        // 选项卡切换
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            document.getElementById('result').innerHTML = '';
        }
        
        // 单曲解析
        async function parseSong() {
            const songId = document.getElementById('songId').value;
            const level = document.getElementById('level').value;
            const resultDiv = document.getElementById('result');
            
            if (!songId.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入歌曲ID</h3></div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result loading"><h3>🔄 正在解析...</h3></div>';
            
            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: level })
                });
                
                const data = await response.json();
                
                if (data.status === 200) {
                    const isLossless = data.debug?.isLossless;
                    const qualityIcon = isLossless ? '🎉' : '🎵';
                    
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>\${qualityIcon} 解析成功</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">🎼 歌曲名</div>
                                    <div>\${data.name}</div>
                                </div>
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">🎤 歌手</div>
                                    <div>\${data.ar_name}</div>
                                </div>
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">💿 专辑</div>
                                    <div>\${data.al_name}</div>
                                </div>
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">📦 文件大小</div>
                                    <div>\${data.size}</div>
                                </div>
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">📊 比特率</div>
                                    <div>\${data.br || '未知'} bps</div>
                                </div>
                                <div class="info-item">
                                    <div style="font-weight: 600; margin-bottom: 5px;">🎧 音质等级</div>
                                    <div>\${data.level}</div>
                                </div>
                            </div>
                            \${data.url ? \`<a href="\${data.url}" target="_blank" class="download-link">⬇️ 下载音乐文件</a>\` : ''}
                            \${data.note ? \`<p style="margin-top: 15px;"><strong>💡 \${data.note}</strong></p>\` : ''}
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 解析失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 网络请求失败</h3><p>\${error.message}</p></div>\`;
            }
        }
        
        // 搜索音乐
        async function searchMusic() {
            const keyword = document.getElementById('searchKeyword').value;
            const searchType = document.getElementById('searchType').value;
            const resultDiv = document.getElementById('result');
            const searchResultsDiv = document.getElementById('searchResults');
            
            if (!keyword.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入搜索关键词</h3></div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result loading"><h3>🔍 正在搜索...</h3></div>';
            searchResultsDiv.style.display = 'none';
            
            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ keyword, type: searchType })
                });
                
                const data = await response.json();
                
                if (data.status === 200) {
                    resultDiv.innerHTML = \`<div class="result success"><h3>🔍 搜索成功</h3><p>找到 \${data.total} 个结果</p></div>\`;
                    
                    if (searchType === 'songs' && data.songs) {
                        let resultsHtml = '';
                        data.songs.forEach(song => {
                            resultsHtml += \`
                                <div class="search-item">
                                    <div class="song-info">
                                        <div class="song-name">\${song.name}</div>
                                        <div class="song-artist">\${song.artists.map(a => a.name).join('/')}</div>
                                    </div>
                                    <div class="song-actions">
                                        <button class="action-btn play-btn" onclick="playPreview('\${song.id}', '\${song.name}', '\${song.artists.map(a => a.name).join('/')}')">
                                            🎵 试听
                                        </button>
                                        <button class="action-btn download-btn" onclick="showDownloadOptions('\${song.id}', '\${song.name}', '\${song.artists.map(a => a.name).join('/')}')">
                                            ⬇️ 下载
                                        </button>
                                    </div>
                                </div>
                            \`;
                        });
                        searchResultsDiv.innerHTML = resultsHtml;
                        searchResultsDiv.style.display = 'block';
                    }
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 搜索失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 搜索请求失败</h3><p>\${error.message}</p></div>\`;
            }
        }
        
        // Apple Music风格播放器
        async function playPreview(songId, songName, artist) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result loading"><h3>🎵 正在获取试听链接...</h3></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: 'standard' })
                });

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <div class="music-player">
                                <button class="close-player" onclick="closePlayer()">×</button>
                                <div class="album-art">🎵</div>
                                <div class="song-title">\${songName}</div>
                                <div class="song-artist">\${artist}</div>

                                <div class="player-controls">
                                    <button class="control-btn" onclick="previousTrack()">⏮</button>
                                    <button class="control-btn play-pause-btn" id="playPauseBtn" onclick="togglePlayPause()">▶</button>
                                    <button class="control-btn" onclick="nextTrack()">⏭</button>
                                </div>

                                <div class="progress-container">
                                    <div class="progress-bar" onclick="seekTo(event)">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="time-display">
                                        <span id="currentTime">0:00</span>
                                        <span id="totalTime">0:00</span>
                                    </div>
                                </div>

                                <div class="volume-container">
                                    <span>🔊</span>
                                    <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="70" onchange="setVolume(this.value)">
                                </div>

                                <audio id="audioPlayer" preload="metadata" onloadedmetadata="updateDuration()" ontimeupdate="updateProgress()">
                                    <source src="\${data.url}" type="audio/mpeg">
                                    您的浏览器不支持音频播放。
                                </audio>
                            </div>
                        </div>
                    \`;

                    // 初始化播放器
                    initializePlayer();
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 无法获取试听链接</h3></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 试听失败</h3><p>\${error.message}</p></div>\`;
            }
        }

        // 显示下载选项
        function showDownloadOptions(songId, songName, artist) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = \`
                <div class="result success">
                    <h3>⬇️ 下载 \${songName} - \${artist}</h3>
                    <div class="form-group">
                        <label>选择音质：</label>
                        <select id="downloadQuality" class="quality-selector">
                            <option value="standard">标准音质 (128kbps)</option>
                            <option value="exhigh">极高音质 (320kbps)</option>
                            <option value="lossless" selected>无损音质 (FLAC)</option>
                            <option value="hires">Hi-Res音质</option>
                        </select>
                        <button class="action-btn download-btn" onclick="downloadSingle('\${songId}', '\${songName}', '\${artist}')" style="margin-left: 10px;">
                            开始下载
                        </button>
                    </div>
                </div>
            \`;
        }

        // 单曲下载
        async function downloadSingle(songId, songName, artist) {
            const quality = document.getElementById('downloadQuality').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<div class="result loading"><h3>🔄 正在获取下载链接...</h3></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: quality })
                });

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    // 创建下载链接
                    const filename = \`\${artist} - \${songName}.\${quality === 'lossless' || quality === 'hires' ? 'flac' : 'mp3'}\`;
                    const link = document.createElement('a');
                    link.href = data.url;
                    link.download = filename;
                    link.click();

                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>✅ 下载开始</h3>
                            <p>文件名：\${filename}</p>
                            <p>音质：\${data.level}</p>
                            <p>大小：\${data.size}</p>
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 无法获取下载链接</h3></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 下载失败</h3><p>\${error.message}</p></div>\`;
            }
        }
        
        // 加载歌单/专辑列表
        async function loadPlaylist() {
            const batchType = document.getElementById('batchType').value;
            const batchInput = document.getElementById('batchInput').value;
            const resultDiv = document.getElementById('result');
            const playlistContainer = document.getElementById('playlistContainer');
            const playlistItems = document.getElementById('playlistItems');

            if (!batchInput.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入ID</h3></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result loading"><h3>� 正在加载歌曲列表...</h3></div>';

            try {
                const response = await fetch('/api/playlist-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: batchType, id: batchInput.trim() })
                });

                const data = await response.json();

                if (data.status === 200) {
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>📋 \${data.name}</h3>
                            <p>共 \${data.songs.length} 首歌曲</p>
                        </div>
                    \`;

                    let itemsHtml = '';
                    data.songs.forEach((song, index) => {
                        itemsHtml += \`
                            <div class="playlist-item">
                                <input type="checkbox" class="playlist-checkbox" id="song_\${song.id}" checked>
                                <div class="playlist-info">
                                    <div class="playlist-name">\${song.name}</div>
                                    <div class="playlist-artist">\${song.artist}</div>
                                </div>
                            </div>
                        \`;
                    });

                    playlistItems.innerHTML = itemsHtml;
                    playlistContainer.style.display = 'block';

                    // 存储歌曲数据供下载使用
                    window.currentPlaylistSongs = data.songs;
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 加载失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 加载请求失败</h3><p>\${error.message}</p></div>\`;
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        // 播放器控制函数
        let currentAudio = null;
        let isPlaying = false;

        function initializePlayer() {
            currentAudio = document.getElementById('audioPlayer');
            const volumeSlider = document.getElementById('volumeSlider');

            if (currentAudio && volumeSlider) {
                currentAudio.volume = volumeSlider.value / 100;
            }
        }

        function togglePlayPause() {
            if (!currentAudio) return;

            const playPauseBtn = document.getElementById('playPauseBtn');

            if (isPlaying) {
                currentAudio.pause();
                playPauseBtn.textContent = '▶';
                isPlaying = false;
            } else {
                currentAudio.play();
                playPauseBtn.textContent = '⏸';
                isPlaying = true;
            }
        }

        function previousTrack() {
            // 重新开始播放当前歌曲
            if (currentAudio) {
                currentAudio.currentTime = 0;
            }
        }

        function nextTrack() {
            // 这里可以实现下一首歌曲的逻辑
            console.log('下一首歌曲功能待实现');
        }

        function seekTo(event) {
            if (!currentAudio) return;

            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const percent = (event.clientX - rect.left) / rect.width;
            currentAudio.currentTime = percent * currentAudio.duration;
        }

        function setVolume(value) {
            if (currentAudio) {
                currentAudio.volume = value / 100;
            }
        }

        function updateDuration() {
            if (!currentAudio) return;

            const totalTime = document.getElementById('totalTime');
            if (totalTime) {
                totalTime.textContent = formatTime(currentAudio.duration);
            }
        }

        function updateProgress() {
            if (!currentAudio) return;

            const progressFill = document.getElementById('progressFill');
            const currentTime = document.getElementById('currentTime');

            if (progressFill && currentTime) {
                const percent = (currentAudio.currentTime / currentAudio.duration) * 100;
                progressFill.style.width = percent + '%';
                currentTime.textContent = formatTime(currentAudio.currentTime);
            }
        }

        function formatTime(seconds) {
            if (isNaN(seconds)) return '0:00';

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return \`\${minutes}:\${remainingSeconds.toString().padStart(2, '0')}\`;
        }

        function closePlayer() {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
                isPlaying = false;
            }

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '';
        }

        // 下载选中歌曲
        async function downloadSelected() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
            const selectedSongs = Array.from(checkboxes).map(cb => {
                const songId = cb.id.replace('song_', '');
                return window.currentPlaylistSongs.find(song => song.id.toString() === songId);
            }).filter(song => song);

            if (selectedSongs.length === 0) {
                document.getElementById('result').innerHTML = '<div class="result error"><h3>❌ 请选择要下载的歌曲</h3></div>';
                return;
            }

            const batchLevel = document.getElementById('batchLevel').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = \`<div class="result loading"><h3>📦 正在下载 \${selectedSongs.length} 首歌曲...</h3><p>请耐心等待</p></div>\`;

            try {
                const response = await fetch('/api/batch-download', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        songs: selectedSongs,
                        level: batchLevel
                    })
                });

                const data = await response.json();

                if (data.status === 200) {
                    if (data.downloadType === 'zip') {
                        // 压缩包下载
                        const link = document.createElement('a');
                        link.href = data.zipUrl;
                        link.download = data.zipFilename;
                        link.click();

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>已打包为压缩文件：\${data.zipFilename}</p>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    } else {
                        // 单独下载
                        data.results.forEach(result => {
                            if (result.success) {
                                const link = document.createElement('a');
                                link.href = result.url;
                                link.download = \`\${result.artist} - \${result.name}.\${result.format}\`;
                                link.click();
                            }
                        });

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    }
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载请求失败</h3><p>\${error.message}</p></div>\`;
            }
        }
        
        // 回车键支持
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('songId').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') parseSong();
            });
            document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') searchMusic();
            });
            
            console.log('🦕 ${isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析器已加载');
        });
    </script>
</body>
</html>
`;

// 请求处理函数
async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const pathname = url.pathname;

  // CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    if (pathname === '/') {
      return new Response(getHomePage(), {
        headers: { 'Content-Type': 'text/html; charset=utf-8', ...corsHeaders }
      });

    } else if (pathname === '/api/song' && req.method === 'POST') {
      // 单曲解析
      const body = await req.json();
      const { ids, url: songUrl, level = 'standard' } = body;
      const songId = ids || songUrl;

      if (!songId) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供歌曲ID'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      console.log(`🎵 [${isDenoDeploy ? 'Deploy' : 'Local'}] 解析歌曲: ${songId}, 音质: ${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      // 测试Cookie有效性
      console.log('🍪 测试Cookie有效性...');
      const cookieValid = await api.testCookie(cookies);
      console.log('🍪 Cookie测试结果:', cookieValid ? '✅ 有效' : '❌ 无效');

      const result = await api.url_v1(songId, level as QualityLevel, cookies);

      if (!result?.data?.[0]) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲信息'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const songData = result.data[0];

      if (!songData.url) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲URL，可能是版权限制或需要会员权限'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      // 获取歌曲详情
      let songInfo: any = {};
      try {
        songInfo = await api.getSongDetail(songId);
      } catch (error) {
        console.warn('获取歌曲详情失败:', error.message);
      }

      const responseData: SongInfo = {
        status: 200,
        name: songInfo?.name || `歌曲ID: ${songId}`,
        ar_name: songInfo?.ar?.map((artist: any) => artist.name).join('/') || '网易云音乐',
        al_name: songInfo?.al?.name || '专辑信息',
        level: api.getQualityName(level as QualityLevel),
        size: api.formatFileSize(songData.size),
        url: songData.url.replace('http://', 'https://'),
        br: songData.br,
        pic: songInfo?.al?.picUrl || '',
        debug: {
          requestedLevel: level,
          actualBr: songData.br,
          isLossless: api.isLosslessQuality(songData.br),
          environment: isDenoDeploy ? 'Deno Deploy' : 'Local'
        }
      };

      if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
        responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
      } else if (level === 'lossless') {
        responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
      }

      return new Response(JSON.stringify(responseData), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });

    } else if (pathname === '/api/search' && req.method === 'POST') {
      // 搜索功能
      const body = await req.json();
      const { keyword, type = 'songs' } = body;

      if (!keyword) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供搜索关键词'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      console.log(`🔍 [${isDenoDeploy ? 'Deploy' : 'Local'}] 搜索: ${keyword}, 类型: ${type}`);

      try {
        let result;
        if (type === 'songs') {
          result = await api.searchSongs(keyword, config.SEARCH_LIMIT);
          return new Response(JSON.stringify({
            status: 200,
            type: 'songs',
            total: result.songCount,
            songs: result.songs
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        } else if (type === 'artists') {
          result = await api.searchArtists(keyword, config.SEARCH_LIMIT);
          return new Response(JSON.stringify({
            status: 200,
            type: 'artists',
            total: result.artistCount,
            artists: result.artists
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        } else {
          throw new Error('不支持的搜索类型');
        }
      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `搜索失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/playlist-info' && req.method === 'POST') {
      // 获取歌单/专辑信息
      const body = await req.json();
      const { type, id } = body;

      console.log(`📋 [${isDenoDeploy ? 'Deploy' : 'Local'}] 获取歌单信息: 类型=${type}, ID=${id}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result;

        if (type === 'album') {
          result = await api.getAlbumInfo(id, cookies);
        } else if (type === 'playlist') {
          result = await api.getPlaylistInfo(id, cookies);
        } else {
          throw new Error('无效的类型参数');
        }

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `获取歌单信息失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/batch-download' && req.method === 'POST') {
      // 新的批量下载API
      const body = await req.json();
      const { songs, level } = body;

      console.log(`📦 [${isDenoDeploy ? 'Deploy' : 'Local'}] 批量下载: ${songs.length} 首歌曲, 音质=${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        const result = await api.batchDownloadSongs(songs, level as QualityLevel, cookies);

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `批量下载失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/batch' && req.method === 'POST') {
      // 批量下载
      const body = await req.json();
      const { type, level = 'standard', songIds, albumId, playlistId } = body;

      console.log(`📦 [${isDenoDeploy ? 'Deploy' : 'Local'}] 批量下载: 类型=${type}, 音质=${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result;

        if (type === 'songs' && songIds && Array.isArray(songIds)) {
          result = await api.batchDownload(songIds, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else if (type === 'album' && albumId) {
          result = await api.downloadAlbum(albumId, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else if (type === 'playlist' && playlistId) {
          result = await api.downloadPlaylist(playlistId, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else {
          throw new Error('无效的批量下载参数');
        }

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `批量下载失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else {
      return new Response('Not Found', {
        status: 404,
        headers: corsHeaders
      });
    }

  } catch (error) {
    console.error(`❌ [${isDenoDeploy ? 'Deploy' : 'Local'}] 服务器错误:`, error);
    return new Response(JSON.stringify({
      status: 500,
      error: `服务器错误: ${error.message}`
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 启动信息
console.log(`🦕 ${isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析服务启动`);
console.log(`🌐 环境: ${isDenoDeploy ? 'Deno Deploy' : '本地环境'}`);
console.log(`🔍 Cookie状态: ${config.NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 有效' : '❌ 无效'}`);
console.log(`🎵 支持功能: 单曲解析、在线搜索、批量下载`);

// 根据环境启动服务
if (isDenoDeploy) {
  // Deno Deploy环境 - 导出在文件末尾
  console.log(`🌐 Deno Deploy模式 - 等待请求`);
} else {
  // 本地环境
  const server = Deno.serve({ port: config.PORT }, handler);
  console.log(`📡 本地服务地址: http://localhost:${config.PORT}`);
}

// Deno Deploy导出（必须在顶层）
export default { fetch: handler };
