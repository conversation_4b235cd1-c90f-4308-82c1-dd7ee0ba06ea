# 🦕 Deno网易云音乐解析器 - 快速启动

## 🌐 Deno Deploy部署（推荐）

### 一键部署到全球CDN

1. **Fork仓库到您的GitHub**
2. **访问 https://dash.deno.com/**
3. **创建新项目**：
   - 选择 "Deploy from GitHub repository"
   - 选择您fork的仓库
   - 入口文件设置为：`main.ts`
4. **配置环境变量**：
   - 添加 `NETEASE_COOKIE`（您的网易云音乐Cookie）
5. **完成部署**：
   - 自动获得全球CDN加速的访问地址

---

## 💻 本地运行

### 1. 安装Deno

**Windows (PowerShell):**
```powershell
irm https://deno.land/install.ps1 | iex
```

**macOS/Linux:**
```bash
curl -fsSL https://deno.land/install.sh | sh
```

### 2. 配置Cookie

```bash
export NETEASE_COOKIE="MUSIC_U=your_cookie;os=pc;appver=8.9.75;"
```

### 3. 启动服务

```bash
# 使用启动脚本
.\start.ps1

# 或直接运行
deno task start
```

### 4. 访问服务

打开浏览器访问：`http://localhost:3002`

---

## 🍪 获取Cookie

1. 登录 https://music.163.com/
2. 打开开发者工具 (F12)
3. 切换到 Network 标签，刷新页面
4. 找到任意请求，复制请求头中的 Cookie 字段

---

## ✨ 功能特性

- 🎵 **单曲解析** - 支持无损音质解析
- 🔍 **在线搜索** - 搜索歌曲和歌手
- 📦 **批量下载** - 专辑、歌单批量下载
- 🌐 **全平台** - 本地运行 + Deno Deploy部署
- 📱 **响应式** - 支持移动端访问

---

## 🎯 项目结构

```
MusicApi-Deno/
├── main.ts              # 主入口文件（本地+Deploy通用）
├── netease-eapi.ts      # 网易云EAPI核心实现
├── deno.json            # Deno配置文件
├── start.ps1            # Windows启动脚本
├── README.md            # 详细文档
├── DEPLOY.md            # 部署指南
└── .github/workflows/   # GitHub Actions自动部署
```

---

## 🚀 一键体验

**本地快速启动：**
```bash
git clone <repository-url>
cd MusicApi-Deno
export NETEASE_COOKIE="your_cookie"
deno task start
```

**Deno Deploy部署：**
1. Fork仓库
2. 连接到Deno Deploy
3. 设置环境变量
4. 自动部署完成

享受现代化的音乐解析体验！🎵
