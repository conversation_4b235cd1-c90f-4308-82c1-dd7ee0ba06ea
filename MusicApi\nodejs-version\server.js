/**
 * 本地Node.js服务器 - 测试无损音质解析
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const NeteaseEAPI = require('./netease-eapi');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// 创建EAPI实例
const api = new NeteaseEAPI();

// 您的Cookie配置
const NETEASE_COOKIE = 'MUSIC_U=1eb9ce22024bb666e99b6743b2222f29ef64a9e88fda0fd5754714b900a5d70d993166e004087dd3b95085f6a85b059f5e9aba41e3f2646e3cebdbec0317df58c119e5;os=pc;appver=8.9.75;';

// 主页路由
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Node.js网易云音乐解析器</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .container { background: #f5f5f5; padding: 20px; border-radius: 10px; }
            .form-group { margin: 15px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin-top: 20px; padding: 15px; background: white; border-radius: 5px; }
            .success { border-left: 4px solid #28a745; }
            .error { border-left: 4px solid #dc3545; }
            .loading { color: #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎵 Node.js网易云音乐解析器</h1>
            <p>测试无损音质解析功能</p>
            
            <form id="parseForm">
                <div class="form-group">
                    <label for="songId">歌曲ID:</label>
                    <input type="text" id="songId" name="songId" value="186016" placeholder="请输入歌曲ID">
                </div>
                
                <div class="form-group">
                    <label for="level">音质等级:</label>
                    <select id="level" name="level">
                        <option value="standard">标准音质 (128kbps)</option>
                        <option value="exhigh">极高音质 (320kbps)</option>
                        <option value="lossless" selected>无损音质 (FLAC)</option>
                        <option value="hires">Hi-Res音质</option>
                    </select>
                </div>
                
                <button type="submit">🔍 解析歌曲</button>
            </form>
            
            <div id="result"></div>
        </div>

        <script>
            document.getElementById('parseForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const songId = document.getElementById('songId').value;
                const level = document.getElementById('level').value;
                const resultDiv = document.getElementById('result');
                
                resultDiv.innerHTML = '<div class="result loading">🔄 正在解析，请稍候...</div>';
                
                try {
                    const response = await fetch('/api/netease/song', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ ids: songId, level: level })
                    });
                    
                    const data = await response.json();
                    
                    if (data.status === 200) {
                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>✅ 解析成功</h3>
                                <p><strong>歌曲名:</strong> \${data.name}</p>
                                <p><strong>歌手:</strong> \${data.ar_name}</p>
                                <p><strong>专辑:</strong> \${data.al_name}</p>
                                <p><strong>音质:</strong> \${data.level}</p>
                                <p><strong>大小:</strong> \${data.size}</p>
                                <p><strong>比特率:</strong> \${data.br || '未知'}</p>
                                \${data.url ? \`<p><strong>下载链接:</strong> <a href="\${data.url}" target="_blank">点击下载</a></p>\` : ''}
                                \${data.note ? \`<p><strong>注意:</strong> \${data.note}</p>\` : ''}
                                \${data.debug ? \`<details><summary>调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                            </div>
                        \`;
                    } else {
                        resultDiv.innerHTML = \`
                            <div class="result error">
                                <h3>❌ 解析失败</h3>
                                <p>\${data.error || '未知错误'}</p>
                                \${data.debug ? \`<details><summary>调试信息</summary><pre>\${JSON.stringify(data.debug, null, 2)}</pre></details>\` : ''}
                            </div>
                        \`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <h3>❌ 请求失败</h3>
                            <p>\${error.message}</p>
                        </div>
                    \`;
                }
            });
        </script>
    </body>
    </html>
  `);
});

// 网易云歌曲解析API
app.post('/api/netease/song', async (req, res) => {
  try {
    const { ids, url: songUrl, level = 'standard' } = req.body;
    const songId = ids || songUrl;
    
    if (!songId) {
      return res.status(400).json({
        status: 400,
        error: '必须提供歌曲ID'
      });
    }
    
    console.log(\`🎵 开始解析歌曲: \${songId}, 音质: \${level}\`);
    
    // 解析Cookie
    const parsedCookies = api.parseCookie(NETEASE_COOKIE);
    const cookies = api.createFullCookieObject(parsedCookies);
    
    // 调用EAPI
    const result = await api.url_v1(songId, level, cookies);
    
    console.log('📡 EAPI响应:', JSON.stringify(result, null, 2));
    
    if (!result || !result.data || !Array.isArray(result.data) || result.data.length === 0) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲信息',
        debug: { result }
      });
    }
    
    const songData = result.data[0];
    
    if (!songData.url) {
      return res.status(400).json({
        status: 400,
        error: '无法获取歌曲URL，可能是版权限制或需要会员权限',
        debug: { songData }
      });
    }
    
    // 获取歌曲详细信息（可选）
    let songInfo = {};
    try {
      // 这里可以添加获取歌曲名称的逻辑
      songInfo.name = \`歌曲ID: \${songId}\`;
      songInfo.ar_name = '网易云音乐';
      songInfo.al_name = '专辑信息';
    } catch (nameError) {
      console.warn('获取歌曲信息失败:', nameError.message);
    }
    
    // 音质等级中文名称
    const qualityNames = {
      'standard': '标准音质',
      'exhigh': '极高音质', 
      'lossless': '无损音质',
      'hires': 'Hi-Res音质',
      'sky': '沉浸环绕声',
      'jyeffect': '高清环绕声',
      'jymaster': '超清母带'
    };
    
    // 构建响应
    const responseData = {
      status: 200,
      name: songInfo.name || \`歌曲ID: \${songId}\`,
      ar_name: songInfo.ar_name || '未知歌手',
      al_name: songInfo.al_name || '未知专辑',
      level: qualityNames[level] || '未知音质',
      size: songData.size ? \`\${(songData.size / 1024 / 1024).toFixed(2)}MB\` : '未知大小',
      url: songData.url.replace('http://', 'https://'),
      br: songData.br,
      debug: {
        requestedLevel: level,
        actualBr: songData.br,
        actualSize: songData.size,
        code: songData.code,
        type: songData.type,
        isLossless: songData.br && songData.br >= 900000
      }
    };
    
    // 检查是否真的获取到了无损音质
    if (level === 'lossless' && songData.br && songData.br >= 900000) {
      responseData.note = \`🎉 成功获取无损音质! 比特率: \${songData.br}\`;
      console.log(\`🎉 成功获取无损音质! 比特率: \${songData.br}\`);
    } else if (level === 'lossless') {
      responseData.note = \`⚠️ 请求无损但获取到较低音质，比特率: \${songData.br || '未知'}\`;
      console.log(\`⚠️ 请求无损但获取到较低音质，比特率: \${songData.br || '未知'}\`);
    }
    
    res.json(responseData);
    
  } catch (error) {
    console.error('❌ 解析失败:', error);
    res.status(500).json({
      status: 500,
      error: \`解析失败: \${error.message}\`,
      debug: { error: error.toString() }
    });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(\`🚀 Node.js网易云音乐解析服务器已启动!\`);
  console.log(\`📡 服务地址: http://localhost:\${PORT}\`);
  console.log(\`🎵 API端点: http://localhost:\${PORT}/api/netease/song\`);
  console.log(\`🔍 Cookie状态: \${NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 包含会员Cookie' : '❌ 缺少会员Cookie'}\`);
  console.log('');
  console.log('请在浏览器中访问 http://localhost:3000 开始测试!');
});
