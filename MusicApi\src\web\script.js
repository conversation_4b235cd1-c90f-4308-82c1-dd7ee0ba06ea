/**
 * 现代化音乐解析工具 - 交互脚本
 */

class MusicParser {
    constructor() {
        this.currentPlatform = 'netease';
        this.currentFeature = 'search';
        this.currentQuality = 'standard';
        this.aplayer = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTheme();
        this.showWelcomeMessage();
    }

    bindEvents() {
        // 平台切换
        document.querySelectorAll('.platform-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchPlatform(e.target.dataset.platform));
        });

        // 功能切换
        document.querySelectorAll('.feature-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchFeature(e.target.dataset.feature));
        });

        // 音质选择
        document.querySelectorAll('.quality-badge').forEach(badge => {
            badge.addEventListener('click', (e) => this.selectQuality(e.target.dataset.quality));
        });

        // 主题切换
        document.getElementById('themeToggle').addEventListener('click', () => this.toggleTheme());

        // 搜索功能
        document.getElementById('searchBtn').addEventListener('click', () => this.searchSongs());
        document.getElementById('searchKeywords').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.searchSongs();
        });

        // 解析功能
        document.getElementById('parseBtn').addEventListener('click', () => this.parseSong());
        document.getElementById('songUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseSong();
        });

        // 歌单解析
        document.getElementById('playlistBtn').addEventListener('click', () => this.parsePlaylist());
        document.getElementById('playlistUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parsePlaylist();
        });

        // 专辑解析
        document.getElementById('albumBtn').addEventListener('click', () => this.parseAlbum());
        document.getElementById('albumUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseAlbum();
        });

        // QQ音乐解析
        document.getElementById('qqParseBtn').addEventListener('click', () => this.parseQQSong());
        document.getElementById('qqSongUrl').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.parseQQSong();
        });
    }

    switchPlatform(platform) {
        this.currentPlatform = platform;
        
        // 更新标签状态
        document.querySelectorAll('.platform-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-platform="${platform}"]`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.platform-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        document.getElementById(`${platform}-panel`).style.display = 'block';

        // 清空结果
        this.clearResults();
        
        this.showNotification(`已切换到${platform === 'netease' ? '网易云音乐' : 'QQ音乐'}`, 'info');
    }

    switchFeature(feature) {
        this.currentFeature = feature;
        
        // 更新按钮状态
        document.querySelectorAll('.feature-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-feature="${feature}"]`).classList.add('active');

        // 切换功能面板
        document.querySelectorAll('.feature-panel').forEach(panel => {
            panel.style.display = 'none';
        });
        document.getElementById(`${feature}-feature`).style.display = 'block';

        // 清空结果
        this.clearResults();
    }

    selectQuality(quality) {
        this.currentQuality = quality;
        
        // 更新音质选择状态
        document.querySelectorAll('.quality-badge').forEach(badge => {
            badge.classList.remove('active');
        });
        document.querySelector(`[data-quality="${quality}"]`).classList.add('active');
    }

    async searchSongs() {
        const keywords = document.getElementById('searchKeywords').value.trim();
        const limit = document.getElementById('searchLimit').value || 10;

        if (!keywords) {
            this.showNotification('请输入搜索关键词', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/netease/search?keywords=${encodeURIComponent(keywords)}&limit=${limit}`);
            const data = await response.json();

            if (data.status === 200 && data.result.length > 0) {
                this.displaySearchResults(data.result);
                this.showNotification(`找到 ${data.result.length} 首歌曲`, 'success');
            } else {
                this.showNotification('未找到相关歌曲', 'info');
                this.clearResults();
            }
        } catch (error) {
            this.showNotification('搜索失败，请重试', 'error');
            console.error('Search error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseSong() {
        const url = document.getElementById('songUrl').value.trim();
        
        if (!url) {
            this.showNotification('请输入歌曲ID或链接', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch('/api/netease/song', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    url: url,
                    level: this.currentQuality,
                    type: 'json'
                })
            });
            
            const data = await response.json();

            if (data.status === 200) {
                this.displaySongResult(data);
                this.showNotification('解析成功！', 'success');
            } else {
                this.showNotification(data.error || '解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('解析失败，请重试', 'error');
            console.error('Parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parsePlaylist() {
        const url = document.getElementById('playlistUrl').value.trim();
        
        if (!url) {
            this.showNotification('请输入歌单ID或链接', 'warning');
            return;
        }

        // 提取歌单ID
        let playlistId = url;
        const idMatch = url.match(/playlist\?id=(\d+)/);
        if (idMatch) playlistId = idMatch[1];

        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/netease/playlist?id=${playlistId}`);
            const data = await response.json();

            if (data.status === 200) {
                this.displayPlaylistResult(data.playlist);
                this.showNotification('歌单解析成功！', 'success');
            } else {
                this.showNotification(data.error || '歌单解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('歌单解析失败，请重试', 'error');
            console.error('Playlist parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseAlbum() {
        const url = document.getElementById('albumUrl').value.trim();
        
        if (!url) {
            this.showNotification('请输入专辑ID或链接', 'warning');
            return;
        }

        // 提取专辑ID
        let albumId = url;
        const idMatch = url.match(/album\?id=(\d+)/);
        if (idMatch) albumId = idMatch[1];

        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/netease/album?id=${albumId}`);
            const data = await response.json();

            if (data.status === 200) {
                this.displayAlbumResult(data.album);
                this.showNotification('专辑解析成功！', 'success');
            } else {
                this.showNotification(data.error || '专辑解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('专辑解析失败，请重试', 'error');
            console.error('Album parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    async parseQQSong() {
        const url = document.getElementById('qqSongUrl').value.trim();
        
        if (!url) {
            this.showNotification('请输入QQ音乐歌曲链接', 'warning');
            return;
        }

        this.showLoading(true);
        
        try {
            const response = await fetch(`/api/qq/song?url=${encodeURIComponent(url)}`);
            const data = await response.json();

            if (data.song && !data.song.msg) {
                this.displayQQSongResult(data);
                this.showNotification('QQ音乐解析成功！', 'success');
            } else {
                this.showNotification(data.song?.msg || 'QQ音乐解析失败', 'error');
            }
        } catch (error) {
            this.showNotification('QQ音乐解析失败，请重试', 'error');
            console.error('QQ parse error:', error);
        } finally {
            this.showLoading(false);
        }
    }

    displaySearchResults(results) {
        const resultArea = document.getElementById('resultArea');
        
        let html = `
            <div class="glass-card fade-in">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-search me-2"></i>搜索结果 (${results.length}首)
                    </h5>
                    <div class="search-results">
        `;

        results.forEach((song, index) => {
            html += `
                <div class="search-result-item slide-in-left" style="animation-delay: ${index * 0.1}s" onclick="musicParser.selectSongFromSearch('${song.id}', '${song.name}')">
                    <div class="d-flex align-items-center">
                        <img src="${song.picUrl || '/default-cover.jpg'}" alt="封面" class="song-cover me-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 fw-bold">${song.name}</h6>
                            <p class="mb-1 text-muted">${song.artists}</p>
                            <small class="text-secondary">${song.album}</small>
                        </div>
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-play-fill me-1"></i>解析
                        </button>
                    </div>
                </div>
            `;
        });

        html += `
                    </div>
                </div>
            </div>
        `;

        resultArea.innerHTML = html;
    }

    selectSongFromSearch(songId, songName) {
        document.getElementById('songUrl').value = songId;
        this.switchFeature('parse');
        this.showNotification(`已选择歌曲：${songName}`, 'info');
        
        // 滚动到解析区域
        document.getElementById('parse-feature').scrollIntoView({ behavior: 'smooth' });
    }

    displaySongResult(data) {
        const resultArea = document.getElementById('resultArea');
        
        const html = `
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="${data.pic}" alt="封面" class="song-cover mb-3" style="width: 120px; height: 120px;" onclick="musicParser.showImageModal('${data.pic}')">
                        </div>
                        <div class="col-md-9">
                            <h4 class="fw-bold mb-2">${data.name}</h4>
                            <p class="mb-2"><span class="badge bg-primary me-2">歌手</span>${data.ar_name}</p>
                            <p class="mb-2"><span class="badge bg-secondary me-2">专辑</span>${data.al_name}</p>
                            <p class="mb-2"><span class="badge bg-success me-2">音质</span>${data.level}</p>
                            <p class="mb-2"><span class="badge bg-warning text-dark me-2">大小</span>${data.size}</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="${data.url}" target="_blank" class="btn btn-primary btn-modern">
                                    <i class="bi bi-download me-2"></i>下载音乐
                                </a>
                                <button class="btn btn-success btn-modern" onclick="musicParser.playMusic('${data.url}', '${data.name}', '${data.ar_name}', '${data.pic}', '${data.lyric}')">
                                    <i class="bi bi-play-fill me-2"></i>在线播放
                                </button>
                            </div>
                        </div>
                    </div>
                    ${data.lyric ? `
                        <div class="mt-4">
                            <h6><i class="bi bi-music-note me-2"></i>歌词</h6>
                            <div class="lyric-box p-3 bg-light rounded" style="max-height: 200px; overflow-y: auto;">
                                ${this.formatLyrics(data.lyric, data.tlyric)}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        resultArea.innerHTML = html;
    }

    displayQQSongResult(data) {
        const resultArea = document.getElementById('resultArea');
        const song = data.song;
        const musicUrls = data.music_urls;
        
        let urlsHtml = '';
        Object.entries(musicUrls).forEach(([format, info]) => {
            if (info && info.url) {
                urlsHtml += `
                    <a href="${info.url}" target="_blank" class="btn btn-outline-primary btn-sm me-2 mb-2">
                        ${format.toUpperCase()} (${info.bitrate})
                    </a>
                `;
            }
        });

        const html = `
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <img src="${song.pic}" alt="封面" class="song-cover mb-3" style="width: 120px; height: 120px;" onclick="musicParser.showImageModal('${song.pic}')">
                        </div>
                        <div class="col-md-9">
                            <h4 class="fw-bold mb-2">${song.name}</h4>
                            <p class="mb-2"><span class="badge bg-info me-2">歌手</span>${song.singer}</p>
                            <p class="mb-2"><span class="badge bg-secondary me-2">专辑</span>${song.album}</p>
                            <div class="mb-3">
                                <h6>可用格式：</h6>
                                ${urlsHtml || '<span class="text-muted">暂无可用下载链接</span>'}
                            </div>
                        </div>
                    </div>
                    ${data.lyric && data.lyric.lyric ? `
                        <div class="mt-4">
                            <h6><i class="bi bi-music-note me-2"></i>歌词</h6>
                            <div class="lyric-box p-3 bg-light rounded" style="max-height: 200px; overflow-y: auto;">
                                ${data.lyric.lyric.replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        resultArea.innerHTML = html;
    }

    displayPlaylistResult(playlist) {
        const resultArea = document.getElementById('resultArea');
        
        let tracksHtml = '';
        playlist.tracks.forEach((track, index) => {
            tracksHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <img src="${track.picUrl}" alt="封面" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <div>
                            <h6 class="mb-1">${index + 1}. ${track.name}</h6>
                            <small class="text-muted">${track.artists} - ${track.album}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="musicParser.selectSongFromSearch('${track.id}', '${track.name}')">
                        解析
                    </button>
                </div>
            `;
        });

        const html = `
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${playlist.coverImgUrl}" alt="歌单封面" class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                        <div>
                            <h4 class="fw-bold mb-1">${playlist.name}</h4>
                            <p class="text-muted mb-1">创建者：${playlist.creator}</p>
                            <small class="text-secondary">共 ${playlist.trackCount} 首歌曲</small>
                        </div>
                    </div>
                    ${playlist.description ? `<p class="text-secondary mb-3">${playlist.description}</p>` : ''}
                    <div class="list-group">
                        ${tracksHtml}
                    </div>
                </div>
            </div>
        `;

        resultArea.innerHTML = html;
    }

    displayAlbumResult(album) {
        const resultArea = document.getElementById('resultArea');
        
        let songsHtml = '';
        album.songs.forEach((song, index) => {
            songsHtml += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <img src="${song.picUrl}" alt="封面" class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <div>
                            <h6 class="mb-1">${index + 1}. ${song.name}</h6>
                            <small class="text-muted">${song.artists}</small>
                        </div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm" onclick="musicParser.selectSongFromSearch('${song.id}', '${song.name}')">
                        解析
                    </button>
                </div>
            `;
        });

        const html = `
            <div class="result-card fade-in">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <img src="${album.coverImgUrl}" alt="专辑封面" class="me-3" style="width: 80px; height: 80px; object-fit: cover; border-radius: 8px;">
                        <div>
                            <h4 class="fw-bold mb-1">${album.name}</h4>
                            <p class="text-muted mb-1">艺术家：${album.artist}</p>
                            <small class="text-secondary">共 ${album.songs.length} 首歌曲</small>
                        </div>
                    </div>
                    ${album.description ? `<p class="text-secondary mb-3">${album.description}</p>` : ''}
                    <div class="list-group">
                        ${songsHtml}
                    </div>
                </div>
            </div>
        `;

        resultArea.innerHTML = html;
    }

    formatLyrics(lyric, tlyric) {
        if (!lyric) return '';
        
        let formatted = lyric.replace(/\n/g, '<br>');
        
        // 如果有翻译歌词，进行合并处理
        if (tlyric) {
            // 这里可以添加歌词合并逻辑
            // 暂时简单处理
            formatted = lyric.replace(/\n/g, '<br>');
        }
        
        return formatted;
    }

    playMusic(url, name, artist, cover, lyric) {
        // 销毁现有播放器
        if (this.aplayer) {
            this.aplayer.destroy();
        }

        // 显示浮动播放器
        const floatingPlayer = document.getElementById('floatingPlayer');
        floatingPlayer.style.display = 'block';
        floatingPlayer.classList.add('bounce-in');

        // 创建新播放器
        this.aplayer = new APlayer({
            container: document.getElementById('aplayer'),
            lrcType: 1,
            audio: [{
                name: name,
                artist: artist,
                url: url,
                cover: cover,
                lrc: lyric || ''
            }]
        });

        this.showNotification('开始播放音乐', 'success');
    }

    showImageModal(imageUrl) {
        document.getElementById('modalImage').src = imageUrl;
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }

    clearResults() {
        document.getElementById('resultArea').innerHTML = '';
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'light';
        this.setTheme(savedTheme);
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        const icon = document.getElementById('themeIcon');
        icon.className = theme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
    }

    showWelcomeMessage() {
        setTimeout(() => {
            this.showNotification('欢迎使用音乐解析工具！支持网易云音乐和QQ音乐', 'info');
        }, 1000);
    }
}

// 初始化应用
const musicParser = new MusicParser();
