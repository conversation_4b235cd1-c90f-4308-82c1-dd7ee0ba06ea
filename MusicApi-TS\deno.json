{"name": "netease-music-api-ts", "version": "1.0.0", "description": "TypeScript版本的网易云音乐API - 支持无损音质解析", "tasks": {"start": "deno run --allow-net --allow-read --allow-write server.ts", "dev": "deno run --allow-net --allow-read --allow-write --watch server.ts", "test": "deno run --allow-net test.ts"}, "imports": {"@std/http": "https://deno.land/std@0.208.0/http/mod.ts", "@std/crypto": "https://deno.land/std@0.208.0/crypto/mod.ts"}, "compilerOptions": {"strict": true, "target": "ES2022", "lib": ["ES2022", "DOM"]}}