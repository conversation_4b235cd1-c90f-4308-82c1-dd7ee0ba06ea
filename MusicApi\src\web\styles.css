/* 现代化音乐解析工具样式 */

/* CSS变量定义 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --netease-color: #c20c0c;
    --qq-color: #12b7f5;
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 8px 15px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
    --glass-bg: rgba(255, 255, 255, 0.95);
    --glass-border: rgba(255, 255, 255, 0.2);
}

/* 暗色主题 */
[data-theme="dark"] {
    --primary-color: #0d6efd;
    --light-color: #212529;
    --dark-color: #f8f9fa;
    --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --card-shadow: 0 4px 6px rgba(255, 255, 255, 0.1);
    --card-shadow-hover: 0 8px 15px rgba(255, 255, 255, 0.2);
    --glass-bg: rgba(33, 37, 41, 0.95);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* 全局样式 */
* {
    transition: var(--transition);
}

body {
    background: var(--gradient-bg);
    color: var(--dark-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    overflow-x: hidden;
}

[data-theme="dark"] body {
    color: var(--light-color);
}

/* 玻璃态效果 */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--transition);
}

.glass-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

/* 平台选择标签 */
.platform-tab {
    border: none;
    background: transparent;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
    margin: 0 8px;
    color: rgba(255, 255, 255, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.platform-tab:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-2px);
}

.platform-tab.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    border-color: var(--primary-color);
}

.platform-tab.netease.active {
    background: var(--netease-color);
    box-shadow: 0 4px 12px rgba(194, 12, 12, 0.3);
    border-color: var(--netease-color);
}

.platform-tab.qq.active {
    background: var(--qq-color);
    box-shadow: 0 4px 12px rgba(18, 183, 245, 0.3);
    border-color: var(--qq-color);
}

/* 功能按钮 */
.feature-btn {
    border-radius: var(--border-radius);
    padding: 15px;
    font-weight: 600;
    transition: var(--transition);
    border: 2px solid var(--primary-color);
    background: transparent;
    color: var(--primary-color);
}

.feature-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.feature-btn.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* 现代化按钮 */
.btn-modern {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    border: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:active {
    transform: translateY(0);
}

/* 音质选择器 */
.quality-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.quality-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    user-select: none;
}

.quality-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.quality-badge.active {
    border-color: white;
    box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}

/* 加载动画 */
.loading-spinner {
    display: none;
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 30px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 结果卡片 */
.result-card {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
    margin-bottom: 20px;
    transition: var(--transition);
    border: none;
}

[data-theme="dark"] .result-card {
    background: #34495e;
    color: var(--light-color);
}

.result-card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

/* 歌曲封面 */
.song-cover {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.song-cover:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(0,0,0,0.2);
}

/* 主题切换按钮 */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    color: var(--dark-color);
}

[data-theme="dark"] .theme-toggle {
    background: rgba(33, 37, 41, 0.9);
    color: var(--light-color);
}

.theme-toggle:hover {
    transform: scale(1.1) rotate(180deg);
    box-shadow: var(--card-shadow-hover);
}

/* 搜索结果项 */
.search-result-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid rgba(0,0,0,0.1);
}

[data-theme="dark"] .search-result-item {
    background: #2c3e50;
    color: var(--light-color);
    border-color: rgba(255,255,255,0.1);
}

.search-result-item:hover {
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

/* 浮动播放器 */
.floating-player {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 320px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow-hover);
    z-index: 1000;
    display: none;
    overflow: hidden;
}

[data-theme="dark"] .floating-player {
    background: #2c3e50;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { 
        opacity: 0; 
        transform: translateX(-50px); 
    }
    to { 
        opacity: 1; 
        transform: translateX(0); 
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { 
        opacity: 0; 
        transform: scale(0.3); 
    }
    50% { 
        opacity: 1; 
        transform: scale(1.05); 
    }
    70% { 
        transform: scale(0.9); 
    }
    100% { 
        opacity: 1; 
        transform: scale(1); 
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .platform-tab {
        padding: 10px 20px;
        margin: 0 4px;
        font-size: 0.9em;
    }
    
    .glass-card {
        margin-bottom: 15px;
    }
    
    .glass-card .card-body {
        padding: 20px 15px;
    }
    
    .floating-player {
        width: calc(100% - 40px);
        left: 20px;
        right: 20px;
        bottom: 10px;
    }
    
    .theme-toggle {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
    
    .quality-selector {
        justify-content: center;
    }
    
    .quality-badge {
        padding: 6px 12px;
        font-size: 0.8em;
    }
    
    .btn-modern {
        padding: 10px 25px;
        font-size: 0.9em;
    }
    
    .song-cover {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 576px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .platform-tab {
        padding: 8px 16px;
        font-size: 0.85em;
    }
    
    .feature-btn {
        padding: 12px;
        font-size: 0.9em;
    }
    
    .glass-card .card-body {
        padding: 15px 10px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.2);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.4);
}

/* 表单控件样式 */
.form-control {
    border-radius: 8px;
    border: 2px solid rgba(0,0,0,0.1);
    padding: 12px 16px;
    transition: var(--transition);
    background: rgba(255,255,255,0.9);
}

[data-theme="dark"] .form-control {
    background: rgba(33, 37, 41, 0.9);
    border-color: rgba(255,255,255,0.1);
    color: var(--light-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background: white;
}

[data-theme="dark"] .form-control:focus {
    background: #2c3e50;
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow-hover);
}

[data-theme="dark"] .modal-content {
    background: #2c3e50;
    color: var(--light-color);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background: rgba(0,0,0,0.9);
    border-radius: 6px;
    padding: 8px 12px;
}

/* 徽章样式 */
.badge {
    border-radius: 6px;
    padding: 6px 10px;
    font-weight: 600;
}

/* 列表组样式 */
.list-group-item {
    border: none;
    border-radius: 8px !important;
    margin-bottom: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: var(--transition);
}

[data-theme="dark"] .list-group-item {
    background: #34495e;
    color: var(--light-color);
}

.list-group-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
