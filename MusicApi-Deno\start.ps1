# Deno网易云音乐解析器启动脚本

Write-Host "🦕 Deno网易云音乐解析器" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

# 检查Deno是否安装
try {
    $denoVersion = deno --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Deno已安装" -ForegroundColor Green
        Write-Host $denoVersion
        Write-Host ""

        # 检查环境变量
        $cookie = $env:NETEASE_COOKIE
        if ($cookie -and $cookie.Contains("MUSIC_U=")) {
            Write-Host "✅ Cookie已配置" -ForegroundColor Green
        } else {
            Write-Host "⚠️  未配置Cookie" -ForegroundColor Yellow
            Write-Host "请设置环境变量：" -ForegroundColor Yellow
            Write-Host '   $env:NETEASE_COOKIE="MUSIC_U=your_cookie;os=pc;appver=8.9.75;"' -ForegroundColor Cyan
            Write-Host ""
        }

        # 启动服务器
        Write-Host "🚀 启动服务器..." -ForegroundColor Yellow
        Write-Host "📡 本地地址: http://localhost:3002" -ForegroundColor Cyan
        Write-Host "🌐 Deno Deploy: 自动适配" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "✨ 功能:" -ForegroundColor Green
        Write-Host "   🎵 单曲解析" -ForegroundColor Cyan
        Write-Host "   🔍 在线搜索" -ForegroundColor Cyan
        Write-Host "   📦 批量下载" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Gray
        Write-Host ""

        deno run --allow-net --allow-env main.ts

    } else {
        throw "Deno未安装"
    }
} catch {
    Write-Host "❌ Deno未安装" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 请安装Deno:" -ForegroundColor Yellow
    Write-Host "   irm https://deno.land/install.ps1 | iex" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "或使用包管理器:" -ForegroundColor Yellow
    Write-Host "   choco install deno" -ForegroundColor Cyan
    Write-Host "   scoop install deno" -ForegroundColor Cyan
}
