/**
 * 简化版 Web 服务器 - 避免模板字符串问题
 * 提供基本的音乐解析工具前端界面
 */

export class WebServer {
  /**
   * 处理Web请求
   * @param {Request} request 
   * @param {Object} env 
   * @returns {Response}
   */
  static async handleWebRequest(request, env) {
    const url = new URL(request.url);
    
    if (url.pathname === '/') {
      return new Response(this.getMainInterfaceHTML(), {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
    
    return this.serve404Page();
  }

  /**
   * 获取主界面HTML内容
   * @returns {string} 完整的HTML内容
   */
  static getMainInterfaceHTML() {
    return '<!DOCTYPE html>' +
      '<html lang="zh-CN">' +
      '<head>' +
        '<meta charset="UTF-8">' +
        '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
        '<title>音乐解析工具 - 支持网易云音乐和QQ音乐</title>' +
        '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">' +
        '<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">' +
        '<style>' + this.getInlineCSS() + '</style>' +
      '</head>' +
      '<body>' +
        '<div class="container py-4">' +
          '<div class="text-center mb-5">' +
            '<h1 class="display-4 fw-bold text-white mb-3">' +
              '<i class="bi bi-music-note-beamed me-3"></i>音乐解析工具' +
            '</h1>' +
            '<p class="lead text-white-50">支持网易云音乐和QQ音乐的高品质音乐解析</p>' +
          '</div>' +
          
          '<div class="row justify-content-center">' +
            '<div class="col-lg-8">' +
              '<div class="glass-card mb-4">' +
                '<div class="card-body">' +
                  '<h5 class="card-title mb-3">' +
                    '<i class="bi bi-search me-2"></i>网易云音乐搜索' +
                  '</h5>' +
                  '<div class="row g-3">' +
                    '<div class="col-md-8">' +
                      '<input type="text" id="searchKeywords" class="form-control" placeholder="输入歌曲名、歌手或专辑名">' +
                    '</div>' +
                    '<div class="col-md-4">' +
                      '<button class="btn btn-success w-100" onclick="searchSongs()">' +
                        '<i class="bi bi-search me-2"></i>搜索' +
                      '</button>' +
                    '</div>' +
                  '</div>' +
                '</div>' +
              '</div>' +
              
              '<div class="glass-card mb-4">' +
                '<div class="card-body">' +
                  '<h5 class="card-title mb-3">' +
                    '<i class="bi bi-link-45deg me-2"></i>单曲解析' +
                  '</h5>' +
                  '<div class="row g-3">' +
                    '<div class="col-md-8">' +
                      '<input type="text" id="songUrl" class="form-control" placeholder="输入歌曲ID或网易云音乐链接">' +
                    '</div>' +
                    '<div class="col-md-4">' +
                      '<button class="btn btn-primary w-100" onclick="parseSong()">' +
                        '<i class="bi bi-download me-2"></i>解析' +
                      '</button>' +
                    '</div>' +
                  '</div>' +
                '</div>' +
              '</div>' +
              
              '<div class="glass-card mb-4">' +
                '<div class="card-body">' +
                  '<h5 class="card-title mb-3">' +
                    '<i class="bi bi-disc me-2"></i>QQ音乐解析' +
                  '</h5>' +
                  '<div class="row g-3">' +
                    '<div class="col-md-8">' +
                      '<input type="text" id="qqSongUrl" class="form-control" placeholder="输入QQ音乐歌曲链接">' +
                    '</div>' +
                    '<div class="col-md-4">' +
                      '<button class="btn btn-info w-100" onclick="parseQQSong()">' +
                        '<i class="bi bi-download me-2"></i>解析' +
                      '</button>' +
                    '</div>' +
                  '</div>' +
                '</div>' +
              '</div>' +
              
              '<div id="loadingSpinner" class="loading-spinner"></div>' +
              '<div id="resultArea"></div>' +
            '</div>' +
          '</div>' +
        '</div>' +
        
        '<footer class="text-center py-4 mt-5">' +
          '<div class="container">' +
            '<p class="text-white-50 mb-0">' +
              '<i class="bi bi-heart-fill text-danger me-1"></i>' +
              '音乐解析工具 &copy; 2025' +
            '</p>' +
          '</div>' +
        '</footer>' +
        
        '<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>' +
        '<script>' + this.getInlineJS() + '</script>' +
      '</body>' +
    '</html>';
  }

  /**
   * 获取内联CSS样式
   * @returns {string} CSS样式内容
   */
  static getInlineCSS() {
    return 'body {' +
      'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);' +
      'color: #343a40;' +
      'font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;' +
      'min-height: 100vh;' +
    '}' +
    '.glass-card {' +
      'background: rgba(255, 255, 255, 0.95);' +
      'backdrop-filter: blur(10px);' +
      'border: 1px solid rgba(255, 255, 255, 0.2);' +
      'border-radius: 12px;' +
      'box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);' +
      'transition: all 0.3s ease;' +
    '}' +
    '.glass-card:hover {' +
      'box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);' +
      'transform: translateY(-2px);' +
    '}' +
    '.loading-spinner {' +
      'display: none;' +
      'width: 50px;' +
      'height: 50px;' +
      'border: 4px solid rgba(255,255,255,0.3);' +
      'border-top: 4px solid #007bff;' +
      'border-radius: 50%;' +
      'animation: spin 1s linear infinite;' +
      'margin: 30px auto;' +
    '}' +
    '@keyframes spin {' +
      '0% { transform: rotate(0deg); }' +
      '100% { transform: rotate(360deg); }' +
    '}' +
    '.result-card {' +
      'background: white;' +
      'border-radius: 12px;' +
      'box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);' +
      'margin-bottom: 20px;' +
      'transition: all 0.3s ease;' +
    '}' +
    '.result-card:hover {' +
      'box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);' +
      'transform: translateY(-2px);' +
    '}';
  }

  /**
   * 获取内联JavaScript代码
   * @returns {string} JavaScript代码内容
   */
  static getInlineJS() {
    return 'function showLoading(show) {' +
      'document.getElementById("loadingSpinner").style.display = show ? "block" : "none";' +
    '}' +
    
    'function showNotification(message, type) {' +
      'alert(message);' +
    '}' +
    
    'async function searchSongs() {' +
      'const keywords = document.getElementById("searchKeywords").value.trim();' +
      'if (!keywords) {' +
        'showNotification("请输入搜索关键词", "warning");' +
        'return;' +
      '}' +
      'showLoading(true);' +
      'try {' +
        'const response = await fetch("/api/netease/search?keywords=" + encodeURIComponent(keywords) + "&limit=10");' +
        'const data = await response.json();' +
        'if (data.status === 200 && data.result.length > 0) {' +
          'displaySearchResults(data.result);' +
          'showNotification("找到 " + data.result.length + " 首歌曲", "success");' +
        '} else {' +
          'showNotification("未找到相关歌曲", "info");' +
        '}' +
      '} catch (error) {' +
        'showNotification("搜索失败，请重试", "error");' +
      '} finally {' +
        'showLoading(false);' +
      '}' +
    '}' +
    
    'async function parseSong() {' +
      'const url = document.getElementById("songUrl").value.trim();' +
      'if (!url) {' +
        'showNotification("请输入歌曲ID或链接", "warning");' +
        'return;' +
      '}' +
      'showLoading(true);' +
      'try {' +
        'const response = await fetch("/api/netease/song", {' +
          'method: "POST",' +
          'headers: { "Content-Type": "application/json" },' +
          'body: JSON.stringify({ url: url, level: "standard", type: "json" })' +
        '});' +
        'const data = await response.json();' +
        'if (data.status === 200) {' +
          'displaySongResult(data);' +
          'showNotification("解析成功！", "success");' +
        '} else {' +
          'showNotification(data.error || "解析失败", "error");' +
        '}' +
      '} catch (error) {' +
        'showNotification("解析失败，请重试", "error");' +
      '} finally {' +
        'showLoading(false);' +
      '}' +
    '}' +
    
    'async function parseQQSong() {' +
      'const url = document.getElementById("qqSongUrl").value.trim();' +
      'if (!url) {' +
        'showNotification("请输入QQ音乐歌曲链接", "warning");' +
        'return;' +
      '}' +
      'showLoading(true);' +
      'try {' +
        'const response = await fetch("/api/qq/song?url=" + encodeURIComponent(url));' +
        'const data = await response.json();' +
        'if (data.song && !data.song.msg) {' +
          'displayQQSongResult(data);' +
          'showNotification("QQ音乐解析成功！", "success");' +
        '} else {' +
          'showNotification(data.song?.msg || "QQ音乐解析失败", "error");' +
        '}' +
      '} catch (error) {' +
        'showNotification("QQ音乐解析失败，请重试", "error");' +
      '} finally {' +
        'showLoading(false);' +
      '}' +
    '}' +
    
    'function displaySearchResults(results) {' +
      'const resultArea = document.getElementById("resultArea");' +
      'let html = "<div class=\\"result-card\\"><div class=\\"card-body\\"><h5>搜索结果 (" + results.length + "首)</h5>";' +
      'results.forEach(function(song, index) {' +
        'html += "<div class=\\"d-flex justify-content-between align-items-center mb-2 p-2 border-bottom\\">";' +
        'html += "<div><strong>" + song.name + "</strong><br><small>" + song.artists + "</small></div>";' +
        'html += "<button class=\\"btn btn-sm btn-outline-primary\\" onclick=\\"selectSong(\'" + song.id + "\')\\">选择</button>";' +
        'html += "</div>";' +
      '});' +
      'html += "</div></div>";' +
      'resultArea.innerHTML = html;' +
    '}' +
    
    'function displaySongResult(data) {' +
      'const resultArea = document.getElementById("resultArea");' +
      'const html = "<div class=\\"result-card\\"><div class=\\"card-body\\">" +' +
        '"<h5>" + data.name + "</h5>" +' +
        '"<p><strong>歌手:</strong> " + data.ar_name + "</p>" +' +
        '"<p><strong>专辑:</strong> " + data.al_name + "</p>" +' +
        '"<p><strong>音质:</strong> " + data.level + "</p>" +' +
        '"<p><strong>大小:</strong> " + data.size + "</p>" +' +
        '"<a href=\\"" + data.url + "\\" target=\\"_blank\\" class=\\"btn btn-primary\\">下载音乐</a>" +' +
        '"</div></div>";' +
      'resultArea.innerHTML = html;' +
    '}' +
    
    'function displayQQSongResult(data) {' +
      'const resultArea = document.getElementById("resultArea");' +
      'let urlsHtml = "";' +
      'Object.entries(data.music_urls).forEach(function([format, info]) {' +
        'if (info && info.url) {' +
          'urlsHtml += "<a href=\\"" + info.url + "\\" target=\\"_blank\\" class=\\"btn btn-sm btn-outline-primary me-2 mb-2\\">" + format.toUpperCase() + "</a>";' +
        '}' +
      '});' +
      'const html = "<div class=\\"result-card\\"><div class=\\"card-body\\">" +' +
        '"<h5>" + data.song.name + "</h5>" +' +
        '"<p><strong>歌手:</strong> " + data.song.singer + "</p>" +' +
        '"<p><strong>专辑:</strong> " + data.song.album + "</p>" +' +
        '"<div><strong>可用格式:</strong><br>" + urlsHtml + "</div>" +' +
        '"</div></div>";' +
      'resultArea.innerHTML = html;' +
    '}' +
    
    'function selectSong(songId) {' +
      'document.getElementById("songUrl").value = songId;' +
      'showNotification("已选择歌曲，请点击解析按钮", "info");' +
    '}';
  }

  /**
   * 服务404页面
   * @returns {Response} 404响应
   */
  static serve404Page() {
    const html = '<!DOCTYPE html>' +
      '<html lang="zh-CN">' +
      '<head>' +
        '<meta charset="UTF-8">' +
        '<title>页面未找到 - 音乐解析工具</title>' +
        '<style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;}</style>' +
      '</head>' +
      '<body>' +
        '<h1>404</h1>' +
        '<p>抱歉，您访问的页面不存在。</p>' +
        '<a href="/">返回首页</a>' +
      '</body>' +
    '</html>';

    return new Response(html, {
      status: 404,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}
