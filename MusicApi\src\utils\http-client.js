/**
 * HTTP客户端工具类
 * 统一的HTTP请求封装，支持网易云和QQ音乐的API调用
 */

export class HttpClient {
  /**
   * 默认请求头
   */
  static get DEFAULT_HEADERS() {
    return {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-site'
    };
  }

  /**
   * 网易云音乐专用请求头
   */
  static get NETEASE_HEADERS() {
    return {
      ...this.DEFAULT_HEADERS,
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
      'Referer': 'https://music.163.com/'
    };
  }

  /**
   * QQ音乐专用请求头
   */
  static get QQ_HEADERS() {
    return {
      ...this.DEFAULT_HEADERS,
      'Referer': 'https://y.qq.com/',
      'Host': 'u.y.qq.com'
    };
  }

  /**
   * 发送GET请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} 响应对象
   */
  static async get(url, options = {}) {
    const requestOptions = {
      method: 'GET',
      headers: { ...this.DEFAULT_HEADERS, ...options.headers },
      ...options
    };

    try {
      const response = await fetch(url, requestOptions);
      return response;
    } catch (error) {
      throw new Error(`GET请求失败: ${error.message}`);
    }
  }

  /**
   * 发送POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} 响应对象
   */
  static async post(url, data = {}, options = {}) {
    const requestOptions = {
      method: 'POST',
      headers: { ...this.DEFAULT_HEADERS, ...options.headers },
      ...options
    };

    // 处理不同类型的请求体
    if (data instanceof FormData) {
      requestOptions.body = data;
      // FormData会自动设置Content-Type，不要手动设置
      delete requestOptions.headers['Content-Type'];
    } else if (typeof data === 'string') {
      requestOptions.body = data;
      requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    } else if (data && typeof data === 'object') {
      // 对于网易云API，通常使用form格式
      const formData = new URLSearchParams();
      for (const [key, value] of Object.entries(data)) {
        formData.append(key, value);
      }
      requestOptions.body = formData;
      requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
    }

    try {
      const response = await fetch(url, requestOptions);
      return response;
    } catch (error) {
      throw new Error(`POST请求失败: ${error.message}`);
    }
  }

  /**
   * 发送网易云音乐API请求
   * @param {string} url - API URL
   * @param {string} params - 加密后的请求参数字符串
   * @param {Object} cookies - Cookie对象
   * @returns {Promise<Object>} 解析后的JSON响应
   */
  static async postNetease(url, params, cookies = {}) {
    const headers = {
      ...this.NETEASE_HEADERS,
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    // 构建完整的Cookie，包含必要的基础Cookie
    const baseCookies = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!"
    };

    // 合并用户Cookie和基础Cookie
    const allCookies = { ...baseCookies, ...cookies };

    if (allCookies && Object.keys(allCookies).length > 0) {
      const cookieString = Object.entries(allCookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ');
      headers['Cookie'] = cookieString;
    }

    // 构建表单数据
    const formData = `params=${encodeURIComponent(params)}`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 使用改进的JSON解析方法
      const result = await this.parseJsonResponse(response);
      return result;
    } catch (error) {
      throw new Error(`网易云API请求失败: ${error.message}`);
    }
  }

  /**
   * 发送QQ音乐API请求
   * @param {string} url - API URL
   * @param {Object} data - 请求数据
   * @param {Object} cookies - Cookie对象
   * @param {string} method - 请求方法
   * @returns {Promise<Object>} 解析后的JSON响应
   */
  static async requestQQ(url, data = {}, cookies = {}, method = 'POST') {
    const headers = { ...this.QQ_HEADERS };
    
    // 添加Cookie
    if (cookies && Object.keys(cookies).length > 0) {
      const cookieString = Object.entries(cookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ');
      headers['Cookie'] = cookieString;
    }

    try {
      let response;
      
      if (method.toUpperCase() === 'GET') {
        // GET请求，将参数添加到URL
        const urlParams = new URLSearchParams(data);
        const fullUrl = `${url}?${urlParams}`;
        response = await this.get(fullUrl, { headers });
      } else {
        // POST请求，发送JSON数据
        headers['Content-Type'] = 'application/json';
        response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(data)
        });
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      return result;
    } catch (error) {
      throw new Error(`QQ音乐API请求失败: ${error.message}`);
    }
  }

  /**
   * 处理重定向请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求选项
   * @returns {Promise<Response>} 响应对象
   */
  static async getWithRedirect(url, options = {}) {
    const requestOptions = {
      method: 'GET',
      headers: { ...this.DEFAULT_HEADERS, ...options.headers },
      redirect: 'manual', // 手动处理重定向
      ...options
    };

    try {
      const response = await fetch(url, requestOptions);
      return response;
    } catch (error) {
      throw new Error(`重定向请求失败: ${error.message}`);
    }
  }

  /**
   * 检查响应状态并解析JSON
   * @param {Response} response - 响应对象
   * @returns {Promise<Object>} 解析后的JSON数据
   */
  static async parseJsonResponse(response) {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    let text = '';
    try {
      text = await response.text();

      // 检查响应是否为空或非JSON格式
      if (!text || text.trim() === '') {
        throw new Error('响应内容为空');
      }

      // 尝试清理可能的非JSON前缀（如JSONP回调）
      let cleanText = text.trim();

      // 移除可能的JSONP回调包装
      const jsonpMatch = cleanText.match(/^[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(\s*(.+)\s*\)\s*;?\s*$/);
      if (jsonpMatch) {
        cleanText = jsonpMatch[1];
      }

      // 移除可能的BOM或其他前缀字符
      cleanText = cleanText.replace(/^\uFEFF/, ''); // BOM
      cleanText = cleanText.replace(/^[^\{\[]*/, ''); // 移除JSON前的非JSON字符

      const data = JSON.parse(cleanText);
      return data;
    } catch (error) {
      // 获取响应文本的前100个字符用于调试
      const preview = text ? text.substring(0, 100) : '无法获取响应内容';
      throw new Error(`JSON解析失败: ${error.message}. 响应预览: ${preview}`);
    }
  }

  /**
   * 添加请求超时
   * @param {Promise} fetchPromise - fetch Promise
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise} 带超时的Promise
   */
  static withTimeout(fetchPromise, timeout = 10000) {
    return Promise.race([
      fetchPromise,
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), timeout);
      })
    ]);
  }
}
